# gnss_data_processor.py
import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import yaml  # 用于读取和修改YAML配置文件

# 解决matplotlib中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def process_gnss_data(excel_path, is_base_station=False):
    """
    处理GNSS数据，将Excel文件转换为DataFrame并进行特征工程

    Args:
        excel_path (str): Excel文件路径
        is_base_station (bool): 是否为基站数据

    Returns:
        pandas.DataFrame: 处理后的数据，如果是基站则返回None
    """
    # 检查是否为基站数据
    if is_base_station:
        print(f"跳过基站数据: {excel_path}")
        return None

    print(f"正在处理文件: {excel_path}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)

        # 检查数据是否为空
        if df.empty:
            print(f"警告: {excel_path} 文件中没有数据")
            return None

        # 输出数据形状和前几行
        print(f"Excel文件数据形状: {df.shape}")
        print("数据前5行:")
        print(df.head())

        # 查看列名，确定日期列和位移列
        print(f"原始列名: {df.columns.tolist()}")

        # 确保日期列是日期时间格式
        date_col = None
        for col in df.columns:
            if '日期' in col or 'date' in col.lower() or '时间' in col:
                date_col = col
                df[col] = pd.to_datetime(df[col], errors='coerce')
                break

        if date_col is None:
            # 尝试找到日期列
            for col in df.columns:
                try:
                    if pd.to_datetime(df[col], errors='coerce').notna().all():
                        df[col] = pd.to_datetime(df[col], errors='coerce')
                        date_col = col
                        break
                except:
                    continue

        if date_col is None:
            print("警告: 未找到日期列，将使用行索引作为日期")
            # 创建日期列，从第一天开始，每天递增
            start_date = datetime(2019, 8, 21)  # 根据数据说明设置起始日期
            df['日期'] = [start_date + timedelta(days=i) for i in range(len(df))]
            date_col = '日期'

        # 检查日期列是否有效
        invalid_dates = df[date_col].isna().sum()
        if invalid_dates > 0:
            print(f"警告: 日期列 '{date_col}' 中有 {invalid_dates} 个无效日期")
            # 删除无效日期的行
            df = df.dropna(subset=[date_col])
            if df.empty:
                print(f"错误: 删除无效日期后没有数据")
                return None

        # 查找位移列
        displacement_cols = []
        for col in df.columns:
            if '△' in col or 'delta' in col.lower() or '位移' in col:
                displacement_cols.append(col)

        # 如果没有找到位移列，尝试使用E、N、H坐标列计算位移
        if not displacement_cols:
            coord_cols = []
            for col in df.columns:
                if col in ['E', 'N', 'H'] or col.endswith('E') or col.endswith('N') or col.endswith('H'):
                    if pd.api.types.is_numeric_dtype(df[col]):
                        coord_cols.append(col)

            # 计算相对位移
            if coord_cols:
                for col in coord_cols:
                    if not df[col].empty and not df[col].isna().all():
                        first_valid = df[col].dropna().iloc[0] if not df[col].dropna().empty else 0
                        df[f'△{col}'] = df[col] - first_valid
                        displacement_cols.append(f'△{col}')

        # 如果仍然没有找到位移列，使用所有数值列
        if not displacement_cols:
            print("警告: 未找到位移列，将使用所有数值列")
            for col in df.columns:
                if pd.api.types.is_numeric_dtype(df[col]) and col != date_col:
                    displacement_cols.append(col)

        print(f"使用的位移列: {displacement_cols}")

        # 设置日期为索引
        df = df.set_index(date_col)

        # 确保数据按日期排序
        df = df.sort_index()

        # 检查数据中的空值
        null_counts = df.isna().sum()
        print("各列空值数量:")
        print(null_counts)

        # 只保留至少有一个非空值的行
        df = df.dropna(how='all')
        if df.empty:
            print("错误: 删除全空行后没有数据")
            return None

        # 确保所有数值列都是数值类型
        for col in df.columns:
            if col != '监测点' and col != '日期':  # 跳过非数值列
                try:
                    # 尝试将列转换为数值类型
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    print(f"将列 {col} 转换为数值类型时出错: {str(e)}")

        # 处理缺失值
        df = df.interpolate(method='linear')

        # 特征工程：计算位移速率
        for col in displacement_cols:
            if col in df.columns:  # 确保列存在
                try:
                    # 确保是数值类型
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    df[f'{col}_速率'] = df[col].diff() / df.index.to_series().diff().dt.total_seconds() * 86400  # 转换为每天的速率
                except Exception as e:
                    print(f"计算 {col} 速率时出错: {str(e)}")

        # 特征工程：计算移动平均
        windows = [3, 7, 14]  # 3天、7天、14天
        for window in windows:
            for col in displacement_cols:
                if col in df.columns:  # 确保列存在
                    try:
                        # 确保是数值类型
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        df[f'{col}_{window}天平均'] = df[col].rolling(window=window).mean()
                    except Exception as e:
                        print(f"计算 {col} {window}天平均时出错: {str(e)}")

        # 特征工程：添加时间特征
        try:
            df['月份'] = df.index.month
            df['星期'] = df.index.dayofweek
        except Exception as e:
            print(f"添加时间特征时出错: {str(e)}")

        # 填充处理后可能产生的新缺失值
        df = df.fillna(method='bfill').fillna(method='ffill')

        # 最终检查
        if df.empty:
            print("错误: 处理后数据为空")
            return None

        print(f"处理后数据形状: {df.shape}")
        return df

    except Exception as e:
        print(f"处理文件 {excel_path} 时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def process_all_gnss_data(input_dir, output_dir, combined_filename="gnss_all_stations.csv"):
    """
    处理目录中的所有GNSS数据文件，排除基站数据，并将所有数据汇总到一个CSV文件

    Args:
        input_dir (str): 输入目录
        output_dir (str): 输出目录
        combined_filename (str): 汇总数据的文件名
    """
    # 确保输入目录存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录 {input_dir} 不存在")
        return None, None

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 用于存储所有处理后的数据
    all_data_frames = []
    station_names = []

    # 检查是否有Excel文件
    excel_files = [f for f in os.listdir(input_dir) if f.endswith(".xlsx") and not f.startswith("~$")]
    if not excel_files:
        print(f"错误: 在 {input_dir} 目录中没有找到Excel文件")
        return None, None

    print(f"找到 {len(excel_files)} 个Excel文件")

    # 处理所有Excel文件
    for file in excel_files:
        # 检查是否为基站数据
        is_base_station = "RCT219040005" in file

        excel_path = os.path.join(input_dir, file)

        if is_base_station:
            print(f"跳过基站数据: {file}")
        else:
            # 获取监测点名称（文件名去除扩展名）
            station_name = os.path.splitext(file)[0]

            # 处理数据
            df = process_gnss_data(excel_path, is_base_station)

            if df is not None and not df.empty:
                print(f"成功处理 {station_name} 数据，形状: {df.shape}")
                # 添加监测点标识列
                df['监测点'] = station_name
                all_data_frames.append(df)
                station_names.append(station_name)
            else:
                print(f"警告: {station_name} 数据处理后为空，将被跳过")

    # 检查是否有有效数据
    if not all_data_frames:
        print("错误: 没有有效的数据可以汇总")
        return None, None

    print(f"共有 {len(all_data_frames)} 个监测点的有效数据")

    # 汇总所有数据
    try:
        # 合并所有数据框
        combined_df = pd.concat(all_data_frames)

        # 在清理前先保存原始数据形状
        original_shape = combined_df.shape
        print(f"原始汇总数据形状: {original_shape}")

        # 检查是否有数据
        if combined_df.empty:
            print("错误: 汇总后的数据为空")
            return None, None

        # 确保'监测点'列不会被移除
        if '监测点' in combined_df.columns:
            # 清理包含空值的列，但保留'监测点'列
            # 计算每列的非空值比例
            non_null_ratio = combined_df.count() / len(combined_df)
            print("各列非空值比例:")
            for col, ratio in non_null_ratio.items():
                print(f"{col}: {ratio:.2f}")

            # 保留非空值比例大于30%的列（进一步降低阈值）
            valid_columns = non_null_ratio[non_null_ratio > 0.3].index.tolist()

            # 确保'监测点'列被保留
            if '监测点' not in valid_columns:
                valid_columns.append('监测点')

            # 应用列筛选
            if valid_columns:
                combined_df = combined_df[valid_columns]
                print(f"保留了 {len(valid_columns)} 列: {valid_columns}")
            else:
                print("警告: 没有列满足非空值比例要求，将保留所有列")

            # 如果清理后没有位移列，则尝试保留原始位移列
            displacement_cols = [col for col in combined_df.columns if ('△' in col or 'delta' in col.lower() or '位移' in col) and col != '监测点']
            if not displacement_cols:
                print("警告: 清理后没有位移列，将尝试保留原始位移列")
                # 查找原始数据中的位移列
                original_displacement_cols = []
                for df in all_data_frames:
                    for col in df.columns:
                        if ('△' in col or 'delta' in col.lower() or '位移' in col) and col != '监测点' and col not in original_displacement_cols:
                            original_displacement_cols.append(col)

                if original_displacement_cols:
                    print(f"找到原始位移列: {original_displacement_cols}")
                    # 重新合并数据，保留监测点列和位移列
                    columns_to_keep = ['监测点'] + original_displacement_cols
                    new_data_frames = []
                    for df in all_data_frames:
                        cols = [col for col in columns_to_keep if col in df.columns]
                        if cols:  # 确保有列可以保留
                            new_data_frames.append(df[cols])

                    if new_data_frames:
                        combined_df = pd.concat(new_data_frames)
                        print(f"重新合并后的数据形状: {combined_df.shape}")

            # 填充缺失值而不是删除行
            combined_df = combined_df.fillna(method='ffill').fillna(method='bfill')
        else:
            print("警告: 数据中没有'监测点'列，将保留所有列")
            # 如果没有监测点列，可能是数据格式有问题，保留所有列
            combined_df = pd.concat(all_data_frames)

        # 最终检查
        if combined_df.empty:
            print("错误: 清理后的数据为空")
            return None, None
    except Exception as e:
        print(f"汇总数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

    # 保存汇总数据前，确保所有数值列都是数值类型
    for col in combined_df.columns:
        if col != '监测点' and col != '日期':  # 跳过非数值列
            try:
                # 尝试将列转换为数值类型
                combined_df[col] = pd.to_numeric(combined_df[col], errors='coerce')
            except Exception as e:
                print(f"将列 {col} 转换为数值类型时出错: {str(e)}")

    # 再次填充可能产生的NaN值
    combined_df = combined_df.fillna(method='ffill').fillna(method='bfill')

    # 保存汇总数据
    combined_output_path = os.path.join(output_dir, combined_filename)
    combined_df.to_csv(combined_output_path)
    print(f"所有监测点数据已汇总到: {combined_output_path}")
    print(f"原始数据形状: {original_shape} -> 清理后数据形状: {combined_df.shape}")

    # 输出保留的列名
    print(f"保留的列: {', '.join(combined_df.columns.tolist())}")

    # 绘制所有监测点的位移曲线
    plt.figure(figsize=(15, 8))

    # 为每个监测点选择不同的颜色
    colors = plt.cm.tab10(np.linspace(0, 1, len(station_names)))

    # 查找所有位移列
    displacement_cols = []
    for col in combined_df.columns:
        if '△' in col or 'delta' in col.lower() or '位移' in col:
            if col not in ['监测点']:  # 排除监测点列
                displacement_cols.append(col)

    if not displacement_cols:
        print("警告: 没有找到位移列，无法绘制位移曲线")
        # 尝试使用所有数值列作为备选
        for col in combined_df.columns:
            if pd.api.types.is_numeric_dtype(combined_df[col]) and col != '监测点':
                displacement_cols.append(col)

        if displacement_cols:
            print(f"将使用以下数值列绘制曲线: {displacement_cols}")

    # 如果仍然没有可用列，则跳过绘图
    if not displacement_cols:
        print("错误: 没有可用的数值列，无法绘制曲线")
        return combined_df, combined_filename

    # 为每个监测点绘制位移曲线
    for i, station in enumerate(station_names):
        try:
            station_data = combined_df[combined_df['监测点'] == station]

            if len(station_data) == 0:
                print(f"警告: 监测点 {station} 在清理后没有数据")
                continue

            # 选择主要位移列（通常是△S或水平位移）
            main_displacement_col = None
            for col in ['△S', '△H', '△E', '△N']:
                if col in displacement_cols:
                    main_displacement_col = col
                    break

            # 如果没有找到主要位移列，使用第一个位移列
            if not main_displacement_col and displacement_cols:
                main_displacement_col = displacement_cols[0]

            if main_displacement_col:
                plt.plot(station_data.index, station_data[main_displacement_col],
                         label=f"{station} - {main_displacement_col}",
                         color=colors[i], linewidth=2)
        except Exception as e:
            print(f"绘制监测点 {station} 数据时出错: {str(e)}")

    plt.title('所有监测点GNSS位移数据对比')
    plt.xlabel('日期')
    plt.ylabel('位移 (mm)')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plot_filename = combined_filename.replace('.csv', '_displacement.png')
    plt.savefig(os.path.join(output_dir, plot_filename), dpi=300)

    return combined_df, combined_filename

def create_gnss_config(output_dir, combined_df, combined_filename, config_filename="gnss_config.yaml"):
    """
    根据处理后的数据创建GNSS配置文件，保留默认配置，只修改数据相关参数

    Args:
        output_dir (str): 输出目录
        combined_df (pandas.DataFrame): 汇总后的数据
        combined_filename (str): 汇总数据的文件名
        config_filename (str): 配置文件名
    """
    # 确保configs目录存在
    config_dir = "./configs"
    os.makedirs(config_dir, exist_ok=True)

    # 获取所有列名
    all_columns = combined_df.columns.tolist()

    # 创建不同类型特征的列表
    pure_displacement_cols = []  # 纯位移列
    velocity_cols = []           # 速率列
    avg_cols = []                # 移动平均列

    # 对所有列进行分类
    for col in all_columns:
        # 排除非数值列
        if col in ['监测点', '日期']:
            continue

        # 分类列
        if '速率' in col or 'velocity' in col.lower() or 'rate' in col.lower():
            velocity_cols.append(col)
        elif '平均' in col or 'avg' in col.lower() or 'mean' in col.lower():
            avg_cols.append(col)
        elif '△' in col or 'delta' in col.lower() or '位移' in col:
            pure_displacement_cols.append(col)

    # 打印分类结果
    print(f"纯位移列: {pure_displacement_cols}")
    print(f"速率列: {velocity_cols}")
    print(f"移动平均列: {avg_cols}")

    # 选择输入特征和标签
    # 默认使用△S作为标签，如果没有则使用第一个位移列
    label_col = '△S' if '△S' in pure_displacement_cols else pure_displacement_cols[0] if pure_displacement_cols else None

    # 输入特征：使用位移、速率和移动平均列，确保没有重复
    input_cols = []
    input_cols.extend(pure_displacement_cols)
    input_cols.extend(velocity_cols)
    input_cols.extend(avg_cols)

    # 移除标签列，避免重复
    if label_col in input_cols:
        input_cols.remove(label_col)

    # 获取列索引
    input_indices = [all_columns.index(col) for col in input_cols]
    label_index = all_columns.index(label_col) if label_col else 0

    try:
        # 尝试读取默认配置文件
        default_config_path = "./configs/config.yaml"
        if os.path.exists(default_config_path):
            print(f"读取默认配置文件: {default_config_path}")
            with open(default_config_path, 'r', encoding='utf-8') as f:
                default_config = f.read()

            # 导入yaml模块
            import yaml

            # 解析默认配置
            default_config_dict = yaml.safe_load(default_config)

            # 修改数据相关参数
            default_config_dict["dataroot"] = f'./data/{combined_filename}'
            default_config_dict["inputs_cols"] = input_indices
            default_config_dict["label_cols"] = [label_index]
            default_config_dict["input_size"] = len(input_indices)
            default_config_dict["output_size"] = 1

            # 将修改后的配置转换回yaml格式
            modified_config = yaml.dump(default_config_dict, sort_keys=False, allow_unicode=True)

            # 添加注释
            config_content = f"""# GNSS数据配置文件 - 自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 基于默认配置文件，只修改了数据相关参数

{modified_config}
"""
        else:
            print(f"警告: 默认配置文件 {default_config_path} 不存在，将创建基本配置")
            # 创建基本配置
            config_content = f"""# GNSS数据配置文件 - 自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 注意: 未找到默认配置文件，这是一个基本配置，可能缺少一些参数

# 数据配置 (Data Configuration)
dataroot: './data/{combined_filename}'  # 汇总的GNSS数据文件路径
inputs_cols: {input_indices}            # 输入特征的列索引
label_cols: [{label_index}]             # 标签列索引（预测目标）
input_size: {len(input_indices)}        # 输入特征维度
output_size: 1                          # 输出维度
split_rate: 0.8                         # 训练集与测试集分割比例
predict_data: 5                         # 预测未来5天的位移

# LSTM模型结构配置 (LSTM Model Structure Configuration)
hidden_size: 64                         # LSTM隐藏层大小
num_layers: 2                           # LSTM层数
dropout_rate: 0.2                       # Dropout比率
batch_first: True                       # 保持批次维度在第一位
seq_len: 14                             # 使用过去14天的数据预测未来

# 训练配置 (Training Configuration)
batch_size: 8                           # 批次大小
lr: 0.001                               # 学习率
epochs: 300                             # 训练轮数

# 学习率调度器配置 (Learning Rate Scheduler Configuration)
step_size: 30                           # 每30个epoch调整学习率
gamma: 0.5                              # 学习率衰减因子

# 路径配置 (Path Configuration)
save_model_path: "./checkpoint"         # 模型保存路径
log_path: "./log"                       # 日志保存路径
"""
    except Exception as e:
        print(f"读取或修改配置文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

        # 创建基本配置作为备选
        config_content = f"""# GNSS数据配置文件 - 自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 注意: 配置文件生成过程中出错，这是一个基本配置，可能缺少一些参数

# 数据配置 (Data Configuration)
dataroot: './data/{combined_filename}'  # 汇总的GNSS数据文件路径
inputs_cols: {input_indices}            # 输入特征的列索引
label_cols: [{label_index}]             # 标签列索引（预测目标）
input_size: {len(input_indices)}        # 输入特征维度
output_size: 1                          # 输出维度
split_rate: 0.8                         # 训练集与测试集分割比例
predict_data: 5                         # 预测未来5天的位移

# LSTM模型结构配置 (LSTM Model Structure Configuration)
hidden_size: 64                         # LSTM隐藏层大小
num_layers: 2                           # LSTM层数
dropout_rate: 0.2                       # Dropout比率
batch_first: True                       # 保持批次维度在第一位
seq_len: 14                             # 使用过去14天的数据预测未来

# 训练配置 (Training Configuration)
batch_size: 8                           # 批次大小
lr: 0.001                               # 学习率
epochs: 300                             # 训练轮数

# 学习率调度器配置 (Learning Rate Scheduler Configuration)
step_size: 30                           # 每30个epoch调整学习率
gamma: 0.5                              # 学习率衰减因子

# 路径配置 (Path Configuration)
save_model_path: "./checkpoint"         # 模型保存路径
log_path: "./log"                       # 日志保存路径
"""

    # 保存配置文件
    config_path = os.path.join(config_dir, config_filename)
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)

    print(f"GNSS配置文件已生成: {config_path}")
    print(f"输入特征: {input_cols}")
    print(f"标签列: {label_col}")

if __name__ == "__main__":
    # 设置输入和输出路径
    input_dir = "./边坡数据集"
    output_dir = "./data"

    # 设置文件名
    combined_filename = "gnss_all_stations.csv"
    config_filename = "gnss_config.yaml"

    # 处理所有GNSS数据，排除基站，并汇总
    result = process_all_gnss_data(input_dir, output_dir, combined_filename)

    # 创建配置文件
    if result is not None:
        combined_df, actual_filename = result
        create_gnss_config(output_dir, combined_df, actual_filename, config_filename)

        print("\n数据处理完成！")
        print(f"1. 所有监测点数据已清理并汇总到 ./data/{actual_filename}")
        print(f"2. 位移曲线图已保存到 ./data/{actual_filename.replace('.csv', '_displacement.png')}")
        print(f"3. 配置文件已生成到 ./configs/{config_filename}（仅包含数据相关配置）")
        print("4. 可以使用以下命令训练模型:")
        print(f"   python scripts/train.py --config ./configs/{config_filename}")
        print("5. 训练完成后，可以使用以下命令预测:")
        print(f"   python scripts/predict.py --config ./configs/{config_filename} --epoch 300 --future")
        print("\n注意:")
        print("- 配置文件仅包含数据相关的配置项，其他参数将使用默认值")
        print("- 数据已经过清理，移除了包含空值的行和列（保留非空值比例>80%的列）")
        print("- 如需调整模型结构或训练参数，请手动编辑配置文件")