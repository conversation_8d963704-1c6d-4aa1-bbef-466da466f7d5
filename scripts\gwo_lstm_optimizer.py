"""
使用灰狼优化算法(GWO)优化LSTM模型参数
"""
import os
import sys
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import yaml
import time
import random
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import config_loader, ensure_directory_exists
from data.data_processor import load_and_process_data
from models.lstm_model import BiLSTMModel
from trainers.trainer import LSTMTrainer
from GWO import GWO

# 设置随机种子，确保结果可复现
def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 定义适应度函数
def fitness_function(params, config, train_dataloader, test_dataloader, device):
    """
    适应度函数，用于评估参数的性能
    
    Args:
        params (numpy.ndarray): 参数数组，包含hidden_size, dropout_rate, lr, weight_decay
        config (dict): 配置字典
        train_dataloader (DataLoader): 训练数据加载器
        test_dataloader (DataLoader): 测试数据加载器
        device (str): 设备，'cuda'或'cpu'
        
    Returns:
        float: 适应度值，这里使用测试集上的MSE
    """
    # 解析参数
    hidden_size = int(params[0])
    dropout_rate = params[1]
    lr = params[2]
    weight_decay = params[3]
    
    # 更新配置
    config["hidden_size"] = hidden_size
    config["dropout_rate"] = dropout_rate
    config["lr"] = lr
    config["weight_decay"] = weight_decay
    
    try:
        # 创建模型
        model = BiLSTMModel(config).to(device)
        
        # 创建训练器
        trainer = LSTMTrainer(model, config, device)
        
        # 训练模型（使用较少的epoch以加快优化过程）
        epochs = 20
        for epoch in range(1, epochs + 1):
            # 训练一个epoch
            train_loss = trainer.train_epoch(train_dataloader)
            
            # 评估模型
            test_loss, _, _ = trainer.evaluate(test_dataloader)
            
            # 如果测试损失很高，提前停止
            if test_loss > 1.0:
                return test_loss
        
        # 返回最终的测试损失作为适应度值
        return test_loss
    except Exception as e:
        print(f"Error in fitness function: {e}")
        return float('inf')  # 如果出错，返回无穷大

def optimize_lstm_params(config_path, max_iter=30, search_agents=10):
    """
    使用灰狼优化算法优化LSTM模型参数
    
    Args:
        config_path (str): 配置文件路径
        max_iter (int): 最大迭代次数
        search_agents (int): 搜索代理数量
        
    Returns:
        tuple: (最优参数, 最优适应度值)
    """
    # 加载配置
    config = config_loader(config_path)
    
    # 设置随机种子
    set_seed(42)
    
    # 加载数据
    print("Loading and processing data...")
    train_dataloader, test_dataloader, min_val, max_val = load_and_process_data(config)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 定义参数搜索空间
    # 参数顺序: [hidden_size, dropout_rate, lr, weight_decay]
    dim = 4
    lb = [16, 0.1, 0.0001, 0.00001]  # 下界
    ub = [256, 0.7, 0.01, 0.001]  # 上界
    
    # 定义目标函数（适应度函数的包装器）
    def objective_function(params):
        return fitness_function(params, config, train_dataloader, test_dataloader, device)
    
    # 使用灰狼优化算法
    print(f"Starting GWO optimization with {search_agents} agents and {max_iter} iterations...")
    start_time = time.time()
    best_params = GWO(objective_function, lb, ub, dim, search_agents, max_iter)
    end_time = time.time()
    
    # 解析最优参数
    hidden_size = int(best_params[0])
    dropout_rate = best_params[1]
    lr = best_params[2]
    weight_decay = best_params[3]
    
    print("\nOptimization completed!")
    print(f"Time taken: {end_time - start_time:.2f} seconds")
    print("\nBest parameters:")
    print(f"hidden_size: {hidden_size}")
    print(f"dropout_rate: {dropout_rate:.4f}")
    print(f"lr: {lr:.6f}")
    print(f"weight_decay: {weight_decay:.8f}")
    
    # 创建优化后的配置文件
    optimized_config = config.copy()
    optimized_config["hidden_size"] = hidden_size
    optimized_config["dropout_rate"] = dropout_rate
    optimized_config["lr"] = lr
    optimized_config["weight_decay"] = weight_decay
    
    # 保存优化后的配置
    optimized_config_path = os.path.join(os.path.dirname(config_path), "gnss_bilstm_gwo_optimized.yaml")
    with open(optimized_config_path, 'w') as f:
        yaml.dump(optimized_config, f, default_flow_style=False)
    
    print(f"\nOptimized configuration saved to: {optimized_config_path}")
    
    return best_params, objective_function(best_params)

def train_with_optimized_params(config_path):
    """
    使用优化后的参数训练模型
    
    Args:
        config_path (str): 优化后的配置文件路径
    """
    # 加载配置
    config = config_loader(config_path)
    
    # 设置随机种子
    set_seed(42)
    
    # 加载数据
    print("Loading and processing data...")
    train_dataloader, test_dataloader, min_val, max_val = load_and_process_data(config)
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 创建模型
    print("Creating model...")
    model = BiLSTMModel(config).to(device)
    
    # 创建训练器
    print("Creating trainer...")
    trainer = LSTMTrainer(model, config, device)
    
    # 训练模型
    print("Starting training...")
    all_y_pred, all_y_true = trainer.train(train_dataloader, test_dataloader)
    
    # 评估模型
    print("Evaluating model...")
    test_loss, test_y_pred, test_y_true = trainer.evaluate(test_dataloader)
    
    # 计算评价指标
    test_y_pred = test_y_pred.cpu().detach().numpy()
    test_y_true = test_y_true[:, 0, :].cpu().detach().numpy()
    
    mse = mean_squared_error(test_y_true, test_y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(test_y_true, test_y_pred)
    
    print("\nTest Metrics:")
    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"R2: {r2:.4f}")
    
    print("\nTraining completed!")

def main():
    """主函数"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Optimize LSTM parameters using GWO')
    parser.add_argument('--config', type=str, default='./configs/gnss_bilstm_config.yaml',
                        help='Path to config file')
    parser.add_argument('--max_iter', type=int, default=30,
                        help='Maximum number of iterations for GWO')
    parser.add_argument('--search_agents', type=int, default=10,
                        help='Number of search agents for GWO')
    parser.add_argument('--train', action='store_true',
                        help='Train with optimized parameters after optimization')
    args = parser.parse_args()
    
    # 优化参数
    best_params, best_fitness = optimize_lstm_params(args.config, args.max_iter, args.search_agents)
    
    # 如果指定了--train参数，则使用优化后的参数训练模型
    if args.train:
        optimized_config_path = os.path.join(os.path.dirname(args.config), "gnss_bilstm_gwo_optimized.yaml")
        train_with_optimized_params(optimized_config_path)

if __name__ == "__main__":
    main()
