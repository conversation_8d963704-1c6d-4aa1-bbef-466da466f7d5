"""
日志工具模块，用于配置和使用日志记录器
"""
import logging
import os
from utils.config import ensure_directory_exists

def setup_logger(log_folder, logger_name="TrainingLogger"):
    """
    配置日志记录器，记录训练过程的信息。

    Args:
        log_folder (str): 日志文件夹路径
        logger_name (str): 日志记录器名称

    Returns:
        logger: 配置好的日志记录器
    """
    ensure_directory_exists(log_folder)
    log_file = os.path.join(log_folder, "training.log")

    # 检查是否已经存在同名的logger
    if logger_name in logging.root.manager.loggerDict:
        return logging.getLogger(logger_name)

    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)

    # 创建文件处理器和控制台处理器
    file_handler = logging.FileHandler(log_file)
    console_handler = logging.StreamHandler()

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger
