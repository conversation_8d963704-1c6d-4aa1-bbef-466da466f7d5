# 风速数据双向LSTM配置文件 - 创建于 2025-05-21
# 基于GNSS配置，针对风速数据进行优化

dataroot: ./data/windspeed.csv
inputs_cols:
- 2  # 标识日期_year
- 3  # 标识日期_month
- 4  # 标识日期_day
- 5  # 标识日期_hour
- 6  # 标识日期_minute
- 7  # 标识日期_dayofweek
- 8  # 标识日期_quarter
- 9  # 标识日期_dayofyear
label_cols:
- 1  # 风速4
split_rate: 0.8
predict_data: 1
input_size: 8        # 输入特征数量
hidden_size: 64      # 隐藏层大小
num_layers: 2        # LSTM层数
output_size: 1       # 输出维度
dropout_rate: 0.3    # Dropout率
batch_first: true
seq_len: 143           # 序列长度，对应约4天的数据（每15分钟一个点）
batch_size: 8       # 批量大小
lr: 0.001            # 学习率
epochs: 500          # 训练轮数
step_size: 20        # 学习率衰减步长
gamma: 0.5           # 学习率衰减因子
weight_decay: 0.0001 # 权重衰减
save_model_path: ./checkpoint
log_path: ./log
model_type: bilstm   # 使用双向LSTM模型
early_stopping: true # 启用早停机制
patience: 20         # 早停耐心值
