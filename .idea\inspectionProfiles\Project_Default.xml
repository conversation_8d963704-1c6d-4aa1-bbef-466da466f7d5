<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="tqdm" />
            <item index="1" class="java.lang.String" itemvalue="scipy" />
            <item index="2" class="java.lang.String" itemvalue="h5py" />
            <item index="3" class="java.lang.String" itemvalue="matplotlib" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="opencv_python" />
            <item index="8" class="java.lang.String" itemvalue="Pillow" />
            <item index="9" class="java.lang.String" itemvalue="PyYAML" />
            <item index="10" class="java.lang.String" itemvalue="deepmerge" />
            <item index="11" class="java.lang.String" itemvalue="urllib3" />
            <item index="12" class="java.lang.String" itemvalue="python-sirp" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N803" />
          <option value="N806" />
          <option value="N812" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="Mi.Crypto" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>