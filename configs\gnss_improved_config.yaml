# GNSS数据改进配置文件 - 手动创建于 2025-05-20
# 包含多项改进以提高模型性能

dataroot: ./data/gnss_all_stations.csv
inputs_cols:
- 7  # △E
- 8  # △N
- 9  # △H
- 11  # △S
- 12  # △E_速率
- 13  # △N_速率
- 14  # △H_速率
- 15  # △S_速率
- 16  # △E_3天平均
- 17  # △N_3天平均
- 18  # △H_3天平均
- 19  # △S_3天平均
- 20  # △E_7天平均
- 21  # △N_7天平均
- 22  # △H_7天平均
- 23  # △S_7天平均
- 24  # △E_14天平均
- 25  # △N_14天平均
- 26  # △H_14天平均
label_cols:
- 10  # △H
split_rate: 0.8
predict_data: 3
input_size: 19
hidden_size: 64       # 从128减小到64，减少过拟合
num_layers: 2         # 从3减小到2，减少过拟合
output_size: 1
dropout_rate: 0.3     # 从0增加到0.3，增加正则化
batch_first: true
seq_len: 10           # 从20减小到10，减少过拟合
batch_size: 8         # 从2增加到8，使梯度更稳定
lr: 0.005
epochs: 500
step_size: 20
gamma: 0.3            # 从0.5减小到0.3，更激进的学习率衰减
weight_decay: 0.00001
save_model_path: ./checkpoint
log_path: ./log
