"""
调试脚本，用于检查CSV文件中的数据类型
"""
import pandas as pd
import numpy as np
import sys

def check_data_types(filepath):
    """
    检查CSV文件中的数据类型
    
    Args:
        filepath (str): CSV文件路径
    """
    try:
        # 读取数据
        print(f"正在读取文件: {filepath}")
        data_df = pd.read_csv(filepath)
        
        # 输出数据形状
        print(f"数据形状: {data_df.shape}")
        
        # 输出列名
        print(f"列名: {data_df.columns.tolist()}")
        
        # 检查每列的数据类型
        print("\n列数据类型:")
        for col in data_df.columns:
            print(f"{col}: {data_df[col].dtype}")
        
        # 检查每列是否包含非数值数据
        print("\n检查非数值数据:")
        for col in data_df.columns:
            if col == '日期' or col == '监测点':  # 跳过非数值列
                continue
                
            # 尝试转换为数值类型
            try:
                pd.to_numeric(data_df[col])
                print(f"{col}: 全部是数值")
            except Exception as e:
                # 找出非数值的行
                non_numeric = data_df[~pd.to_numeric(data_df[col], errors='coerce').notna()]
                if not non_numeric.empty:
                    print(f"{col}: 包含非数值数据，共 {len(non_numeric)} 行")
                    print(f"前5个非数值样本: {non_numeric[col].head().tolist()}")
                    print(f"这些行的索引: {non_numeric.index.tolist()[:5]}")
        
        # 尝试执行归一化操作
        print("\n尝试执行归一化操作:")
        try:
            # 将所有列转换为数值类型
            numeric_df = data_df.copy()
            for col in numeric_df.columns:
                if col != '日期' and col != '监测点':  # 跳过非数值列
                    numeric_df[col] = pd.to_numeric(numeric_df[col], errors='coerce')
            
            # 填充NaN值
            numeric_df = numeric_df.fillna(method='ffill').fillna(method='bfill')
            
            # 执行归一化
            min_vals = np.min(numeric_df.select_dtypes(include=[np.number]).values, axis=0)
            max_vals = np.max(numeric_df.select_dtypes(include=[np.number]).values, axis=0)
            print("归一化操作成功")
        except Exception as e:
            print(f"归一化操作失败: {e}")
        
    except Exception as e:
        print(f"检查数据时出错: {e}")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        filepath = sys.argv[1]
    else:
        filepath = "./data/gnss_all_stations.csv"  # 默认文件路径
    
    # 检查数据
    check_data_types(filepath)
