"""
主入口文件，用于训练和预测LSTM模型
支持单向LSTM和双向LSTM两种模型
"""
import argparse
import torch
import os

from utils.config import config_loader, ensure_directory_exists
from data.data_processor import load_and_process_data
from models.lstm_model import LSTMModel, BiLSTMModel
from trainers.trainer import LSTMTrainer
from scripts.predict import predict_with_model
from utils.visualization import plot_loss_curves, plot_predictions

def main():
    """
    主函数，解析命令行参数并执行相应操作
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='LSTM Model for Time Series Prediction')
    parser.add_argument('--config', type=str, default='./configs/config.yaml',
                        help='Path to config file')
    parser.add_argument('--train', action='store_true',
                        help='Train the model')
    parser.add_argument('--predict', action='store_true',
                        help='Predict using the trained model')
    parser.add_argument('--epoch', type=int, default=None,
                        help='Epoch of model to load for prediction')
    args = parser.parse_args()

    # 如果没有指定操作，则默认进行预测
    if not args.train and not args.predict:
        args.predict = True

    # 加载配置
    config = config_loader(args.config)

    # 确保模型保存目录和日志目录存在
    ensure_directory_exists(config["save_model_path"])
    ensure_directory_exists(config["log_path"])

    # 加载数据
    print("Loading and processing data...")
    train_dataloader, test_dataloader, min_val, max_val = load_and_process_data(config)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 训练模型
    if args.train:
        print("Creating model...")
        # 根据配置选择模型类型
        model_type = config.get("model_type", "lstm").lower()
        if model_type == "bilstm":
            print("Using BiLSTM model...")
            model = BiLSTMModel(config).to(device)
        else:
            print("Using LSTM model...")
            model = LSTMModel(config).to(device)

        print("Creating trainer...")
        trainer = LSTMTrainer(model, config, device)

        print("Starting training...")
        all_y_pred, all_y_true = trainer.train(train_dataloader, test_dataloader)

        print("Plotting loss curves...")
        plot_loss_curves(trainer)

        print("Plotting predictions...")
        plot_predictions(all_y_pred, all_y_true, min_val, max_val, config)

        print("Training completed!")

    # 预测
    if args.predict:
        # 如果没有指定epoch，则使用配置中的epochs
        if args.epoch is None:
            args.epoch = config["epochs"]

        # 确定模型类型
        model_type = config.get("model_type", "lstm").lower()

        # 加载模型
        print(f"Loading {model_type} model from epoch {args.epoch}...")
        from models.lstm_model import load_model
        model = load_model(config, args.epoch, device, model_type=model_type)

        print("Predicting...")
        # 创建保存图表的目录
        folder_name = f"training_epochs_{config['epochs']}_stepsize_{config['step_size']}_gamma_{config['gamma']}"
        save_dir = os.path.join("results", folder_name)
        os.makedirs(save_dir, exist_ok=True)

        # 进行预测
        aligned_preds, _, _ = predict_with_model(model, test_dataloader, min_val, max_val, config, device, save_path=save_dir)

        print("Prediction completed!")

if __name__ == '__main__':
    main()
