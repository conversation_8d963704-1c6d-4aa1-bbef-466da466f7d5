"""
预测脚本，用于使用训练好的LSTM模型进行预测
"""
import argparse
import torch
import os
import sys
import numpy as np
import pandas as pd

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import config_loader
from data.data_processor import load_and_process_data, adjust_batch_first, denormalize_data
from models.lstm_model import load_model
from utils.visualization import plot_predictions, plot_future_predictions, plot_train_test_comparison

def predict_with_model(model, test_dataloader, min_val, max_val, config, device, visualize=True, save_path=None):
    """
    使用模型进行预测

    Args:
        model (nn.Module): 训练好的模型
        test_dataloader (DataLoader): 测试数据加载器
        min_val (numpy.ndarray): 归一化最小值
        max_val (numpy.ndarray): 归一化最大值
        config (dict): 配置字典
        device (str): 设备，'cuda'或'cpu'
        visualize (bool, optional): 是否可视化预测结果
        save_path (str, optional): 图像保存路径，如果为None则不保存

    Returns:
        tuple: (aligned_preds, all_y_pred, all_y_true) 对齐后的预测结果、原始预测结果和真实值
    """
    predict_list = []
    origin_list = []

    model.eval()
    with torch.no_grad():
        for x, y in test_dataloader:
            # 调整批次维度
            x, y = adjust_batch_first(x, y, config["batch_first"])

            # 将数据移动到设备
            x, y = x.to(device), y.to(device)

            # 为每个批次创建新的隐藏状态
            batch_size = x.size(0)
            hidden_size = getattr(model, 'hidden_size', 64)  # 默认值为64
            num_layers = getattr(model, 'num_layers', 2)     # 默认值为2

            # 检查是否是双向LSTM模型
            from models.lstm_model import BiLSTMModel
            if isinstance(model, BiLSTMModel):
                # 双向LSTM的隐藏状态维度是num_layers*2
                h_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(device)
                c_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(device)
            else:
                # 单向LSTM的隐藏状态维度是num_layers
                h_n = torch.zeros(num_layers, batch_size, hidden_size).to(device)
                c_n = torch.zeros(num_layers, batch_size, hidden_size).to(device)

            # 前向传播
            y_pred, _ = model(x, h_n, c_n)

            # 保存预测结果和真实值
            predict_list.append(y_pred.cpu())
            origin_list.append(y.cpu())

    # 合并所有批次的预测结果和真实值
    all_y_true = torch.cat(origin_list, dim=0)
    all_y_pred = torch.cat(predict_list, dim=0)

    # 可视化预测结果
    if visualize:
        aligned_preds = plot_predictions(all_y_pred, all_y_true, min_val, max_val, config, save_path=save_path)
    else:
        # 计算对齐后的预测结果
        predict_steps = config["predict_data"]
        output_size = config["output_size"]

        all_y_pred_np = all_y_pred.numpy()
        all_y_true_np = all_y_true[:, 0, :].numpy()

        time_len = all_y_true_np.shape[0] + predict_steps - 1
        aligned_preds = np.zeros((time_len, output_size))
        counts = np.zeros((time_len, output_size))

        for i in range(all_y_pred_np.shape[0]):
            for t in range(predict_steps):
                aligned_preds[i+t] += all_y_pred_np[i, t]
                counts[i+t] += 1

        aligned_preds /= np.maximum(counts, 1)

        # 将归一化后的数据还原成原数据
        aligned_preds = denormalize_data(aligned_preds, min_val, max_val, config["label_cols"])

    return aligned_preds, all_y_pred, all_y_true

def predict_future_with_model(model, input_sequence, min_val, max_val, config, device, steps=None):
    """
    预测未来数据

    Args:
        model (nn.Module): 训练好的模型
        input_sequence (numpy.ndarray): 输入序列，形状为(seq_len, input_size)
        min_val (numpy.ndarray): 归一化最小值
        max_val (numpy.ndarray): 归一化最大值
        config (dict): 配置字典
        device (str): 设备，'cuda'或'cpu'
        steps (int, optional): 预测步数，如果为None则使用配置中的predict_data

    Returns:
        numpy.ndarray: 预测结果
    """
    # 如果没有指定steps，则使用配置中的predict_data
    if steps is None:
        steps = config["predict_data"]

    # 确保输入序列是正确的形状
    if len(input_sequence.shape) == 2:
        input_sequence = np.expand_dims(input_sequence, axis=0)  # 添加批次维度

    # 转换为张量并移动到设备
    input_tensor = torch.tensor(input_sequence, dtype=torch.float32).to(device)

    # 初始化隐藏状态
    batch_size = input_tensor.size(0)
    hidden_size = getattr(model, 'hidden_size', 64)  # 默认值为64
    num_layers = getattr(model, 'num_layers', 2)     # 默认值为2

    # 检查是否是双向LSTM模型
    from models.lstm_model import BiLSTMModel
    if isinstance(model, BiLSTMModel):
        # 双向LSTM的隐藏状态维度是num_layers*2
        h_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(device)
        c_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(device)
    else:
        # 单向LSTM的隐藏状态维度是num_layers
        h_n = torch.zeros(num_layers, batch_size, hidden_size).to(device)
        c_n = torch.zeros(num_layers, batch_size, hidden_size).to(device)

    # 预测
    model.eval()
    with torch.no_grad():
        output, _ = model(input_tensor, h_n, c_n)

    # 将预测结果转换为NumPy数组
    predictions = output.cpu().numpy()

    # 将归一化后的数据还原成原数据
    predictions = denormalize_data(predictions[0], min_val, max_val, config["label_cols"])

    return predictions

def main():
    """
    主函数，解析命令行参数并进行预测
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Predict using LSTM model')
    parser.add_argument('--config', type=str, default='./configs/config.yaml',
                        help='Path to config file')
    parser.add_argument('--epoch', type=int, default=None,
                        help='Epoch of model to load')
    parser.add_argument('--best', action='store_true',
                        help='Use the best model instead of the last epoch')
    parser.add_argument('--future', action='store_true',
                        help='Predict future data points')
    parser.add_argument('--compare', action='store_true',
                        help='Compare training and testing set predictions to analyze underfitting/overfitting')
    args = parser.parse_args()

    # 加载配置
    config = config_loader(args.config)

    # 如果没有指定epoch，则使用配置中的epochs
    if args.epoch is None:
        args.epoch = config["epochs"]

    # 加载数据
    print("Loading and processing data...")
    train_dataloader, test_dataloader, min_val, max_val = load_and_process_data(config)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载模型
    if args.best:
        print("Loading best model...")
        folder_name = f"training_epochs_{config['epochs']}_stepsize_{config['step_size']}_gamma_{config['gamma']}"

        # 确定模型类型
        model_type = config.get("model_type", "lstm").lower()
        best_model_path = os.path.join(config["save_model_path"], folder_name, f"{model_type}_best_model.pth")

        # 加载最佳模型权重
        checkpoint = torch.load(best_model_path, map_location=device)

        # 创建模型实例
        from models.lstm_model import LSTMModel, BiLSTMModel
        if model_type == "bilstm" or checkpoint.get('model_type') == "bilstm":
            model = BiLSTMModel(config).to(device)
            print("Using BiLSTM model...")
        else:
            model = LSTMModel(config).to(device)
            print("Using LSTM model...")

        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Best model loaded from {best_model_path} (epoch {checkpoint['epoch']})")
    else:
        print(f"Loading model from epoch {args.epoch}...")
        model = load_model(config, args.epoch, device)

    # 创建保存图表的目录
    save_dir = os.path.join("results", folder_name)
    os.makedirs(save_dir, exist_ok=True)

    # 使用测试数据进行预测
    print("Predicting...")
    aligned_preds, all_y_pred, all_y_true = predict_with_model(model, test_dataloader, min_val, max_val, config, device, save_path=save_dir)

    # 如果需要比较训练集和测试集的预测结果
    if args.compare:
        print("\nComparing training and testing set predictions...")

        # 获取训练集预测结果
        print("Predicting on training set...")
        train_aligned_preds, train_all_y_pred, train_all_y_true = predict_with_model(model, train_dataloader, min_val, max_val, config, device, visualize=False)

        # 对比训练集和测试集的预测结果
        plot_train_test_comparison(train_all_y_pred, train_all_y_true, all_y_pred, all_y_true, min_val, max_val, config, save_path=save_dir)

    # 获取列名
    file_path = config["dataroot"]
    label_cols = config["label_cols"]
    header = pd.read_csv(file_path, nrows=0).columns.tolist()
    header = [header[i] for i in label_cols]

    # 打印最后几个预测结果
    predict_data = config["predict_data"]
    print(f"\nLast {predict_data} predictions:")
    for i in range(predict_data):
        print(f"Step {i+1}:", end=" ")
        for j in range(len(header)):
            print(f"{header[j]}: {aligned_preds[-predict_data+i, j]:.4f}", end=" ")
        print()

    # 如果需要预测未来数据
    if args.future:
        print("\nPredicting future data points...")
        # 获取最后一个序列作为输入
        last_sequence = test_dataloader.dataset.inputs[-1]
        future_preds = predict_future_with_model(model, last_sequence, min_val, max_val, config, device)

        print(f"\nFuture {predict_data} predictions:")
        for i in range(predict_data):
            print(f"Step {i+1}:", end=" ")
            for j in range(len(header)):
                print(f"{header[j]}: {future_preds[i, j]:.4f}", end=" ")
            print()

        # 可视化未来预测结果，不使用历史数据
        plot_future_predictions(future_preds, header, save_path=save_dir)

    print("\nPrediction completed!")

if __name__ == '__main__':
    main()
