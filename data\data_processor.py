"""
数据处理模块，用于读取、预处理和加载数据
"""
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader

class LSTMDataset(Dataset):
    """
    LSTM数据集类，用于创建PyTorch数据集
    """
    def __init__(self, inputs, labels):
        """
        初始化数据集

        Args:
            inputs (numpy.ndarray): 输入特征
            labels (numpy.ndarray): 标签/目标值
        """
        self.inputs = inputs
        self.labels = labels

    def __getitem__(self, index):
        """
        获取数据集中的一个样本

        Args:
            index (int): 样本索引

        Returns:
            tuple: (input, label) 输入特征和标签
        """
        input_data = self.inputs[index]
        label = self.labels[index]

        # 检查输入是否为嵌套结构，如果是，转换为正确的数值类型
        if isinstance(input_data, np.ndarray):
            input_data = input_data.astype(np.float32)
        if isinstance(label, np.ndarray):
            label = label.astype(np.float32)

        # 将数据转换为 Tensor
        input_tensor = torch.tensor(input_data)
        label_tensor = torch.tensor(label)

        return input_tensor, label_tensor

    def __len__(self):
        """
        获取数据集长度

        Returns:
            int: 数据集中的样本数量
        """
        return len(self.inputs)


def normalize_data(data):
    """
    将数据逐列归一化到 [0, 1]

    Args:
        data (numpy.ndarray): 二维 NumPy 数组 (samples, features)，原始数据

    Returns:
        tuple: (normalized_data, min_vals, max_vals) 归一化后的数据和每列的最小值、最大值
    """
    try:
        # 检查数据类型
        if not isinstance(data, np.ndarray):
            print(f"警告: 输入数据不是NumPy数组，而是 {type(data)}")
            data = np.array(data, dtype=np.float64)

        # 检查是否包含非数值数据
        if not np.issubdtype(data.dtype, np.number):
            print(f"警告: 输入数据包含非数值类型: {data.dtype}")
            # 尝试转换为浮点数
            data = data.astype(np.float64)

        # 保证输入是二维数组
        if data.ndim == 1:
            data = data[:, np.newaxis]

        # 打印数据形状和类型
        print(f"归一化前数据形状: {data.shape}, 类型: {data.dtype}")

        # 检查是否有NaN或无穷大
        if np.isnan(data).any() or np.isinf(data).any():
            print("警告: 数据中包含NaN或无穷大值，将被替换为0")
            data = np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)

        min_vals = np.min(data, axis=0)  # 每列最小值
        max_vals = np.max(data, axis=0)  # 每列最大值

        # 检查是否有常数列（最大值等于最小值）
        constant_cols = np.where(max_vals == min_vals)[0]
        if len(constant_cols) > 0:
            print(f"警告: 发现 {len(constant_cols)} 个常数列，索引为: {constant_cols}")
            # 为常数列添加小扰动，避免除以零
            for col in constant_cols:
                max_vals[col] += 1e-8

        # 执行归一化
        normalized_data = (data - min_vals) / (max_vals - min_vals + 1e-8)  # 广播机制

        # 检查归一化结果
        if np.isnan(normalized_data).any() or np.isinf(normalized_data).any():
            print("警告: 归一化后数据包含NaN或无穷大值，将被替换为0")
            normalized_data = np.nan_to_num(normalized_data, nan=0.0, posinf=0.0, neginf=0.0)

        return normalized_data, min_vals, max_vals

    except Exception as e:
        print(f"归一化数据时出错: {e}")
        # 打印错误发生位置的数据信息
        import traceback
        traceback.print_exc()

        # 返回原始数据和默认的最小值/最大值
        if isinstance(data, np.ndarray):
            min_vals = np.zeros(data.shape[1] if data.ndim > 1 else 1)
            max_vals = np.ones(data.shape[1] if data.ndim > 1 else 1)
            return data, min_vals, max_vals
        else:
            print("无法处理输入数据，返回空数组")
            return np.array([]), np.array([]), np.array([])


def denormalize_data(normalized_data, min_vals, max_vals, label_cols):
    """
    将归一化后的数据逐列还原到原始范围

    Args:
        normalized_data (numpy.ndarray): 归一化后的数据 (samples, features)
        min_vals (numpy.ndarray): 每列的最小值 (1D 数组)
        max_vals (numpy.ndarray): 每列的最大值 (1D 数组)
        label_cols (list): 标签列索引

    Returns:
        numpy.ndarray: 还原后的数据
    """
    max_vals = np.array(max_vals)
    min_vals = np.array(min_vals)

    if not isinstance(label_cols, list):
        label_cols = [label_cols]

    # 保证输入是二维数组
    if normalized_data.ndim == 1:
        normalized_data = normalized_data[:, np.newaxis]

    original_data = normalized_data * (max_vals[label_cols] - min_vals[label_cols] + 1e-8) + min_vals[label_cols]

    return original_data


def prepare_sequence_data(data, input_cols, label_cols, seq_len, predict_data):
    """
    准备序列数据，将原始数据转换为序列输入和目标输出

    Args:
        data (pandas.DataFrame): 原始数据
        input_cols (list): 输入特征的列索引
        label_cols (list): 标签/目标值的列索引
        seq_len (int): 序列长度
        predict_data (int): 预测步数

    Returns:
        tuple: (x, y) 序列输入和目标输出
    """
    if not isinstance(label_cols, list):
        label_cols = [label_cols]

    inputs = data.iloc[:, input_cols].values
    labels = data.iloc[:, label_cols].values

    x, y = [], []
    for i in range(len(labels) - seq_len - predict_data + 1):
        x.append(inputs[i:i+seq_len, :])
        y.append(labels[i+seq_len:i+seq_len+predict_data, :])

    return np.array(x), np.array(y)


def load_and_process_data(config):
    """
    加载并处理数据

    Args:
        config (dict): 配置字典

    Returns:
        tuple: (train_dl, test_dl, min_val, max_val) 训练数据加载器、测试数据加载器和归一化参数
    """
    try:
        # 从配置中获取参数
        input_cols = config["inputs_cols"]
        label_cols = config["label_cols"]
        seq_len = config["seq_len"]
        split_rate = config["split_rate"]
        predict_data = config["predict_data"]
        filepath = config["dataroot"]
        batch_size = config["batch_size"]

        print(f"加载数据文件: {filepath}")
        print(f"输入列索引: {input_cols}")
        print(f"标签列索引: {label_cols}")

        # 读取数据
        data_df = pd.read_csv(filepath)
        print(f"原始数据形状: {data_df.shape}")
        print(f"列名: {data_df.columns.tolist()}")

        # 检查是否有日期列或非数值列
        non_numeric_cols = []
        for col in data_df.columns:
            if data_df[col].dtype == 'object' or 'date' in col.lower() or '日期' in col:
                non_numeric_cols.append(col)

        if non_numeric_cols:
            print(f"发现非数值列: {non_numeric_cols}")
            # 保存非数值列
            non_numeric_data = {col: data_df[col].copy() for col in non_numeric_cols}
            # 从数据框中移除非数值列
            data_df = data_df.drop(columns=non_numeric_cols)
            print(f"移除非数值列后的数据形状: {data_df.shape}")

        # 将所有列转换为数值类型
        for col in data_df.columns:
            try:
                data_df[col] = pd.to_numeric(data_df[col], errors='coerce')
            except Exception as e:
                print(f"将列 {col} 转换为数值类型时出错: {e}")

        # 填充可能的NaN值
        data_df = data_df.fillna(method='ffill').fillna(method='bfill')

        # 归一化数据
        print("开始归一化数据...")
        data, min_val, max_val = normalize_data(data_df.values)
        data_df = pd.DataFrame(data, columns=data_df.columns)

        # 如果有非数值列，将其添加回数据框
        if non_numeric_cols:
            for col in non_numeric_cols:
                data_df[col] = non_numeric_data[col]

        # 检查列索引是否有效
        if any(col >= data_df.shape[1] for col in input_cols + label_cols):
            print(f"列索引超出范围。数据框列数: {data_df.shape[1]}, 输入列索引: {input_cols}, 标签列索引: {label_cols}")
            raise ValueError("指定的列索引超出csv文件的列范围")

        # 准备序列数据
        print("准备序列数据...")
        x, y = prepare_sequence_data(data_df, input_cols, label_cols, seq_len, predict_data)
        print(f"序列数据形状 - x: {x.shape}, y: {y.shape}")

        # 分割训练集和测试集
        split_index = int(len(x) * split_rate)
        x_train, y_train = x[:split_index], y[:split_index]
        x_test, y_test = x[split_index:], y[split_index:]
        print(f"训练集形状 - x: {x_train.shape}, y: {y_train.shape}")
        print(f"测试集形状 - x: {x_test.shape}, y: {y_test.shape}")

        # 创建数据集和数据加载器
        train_dataset = LSTMDataset(x_train, y_train)
        test_dataset = LSTMDataset(x_test, y_test)

        train_dataloader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True
        )

        test_dataloader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False
        )

        return train_dataloader, test_dataloader, min_val, max_val

    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None


def adjust_batch_first(inputs, labels, batch_first=True):
    """
    调整输入和标签的维度顺序

    Args:
        inputs (torch.Tensor): 输入张量
        labels (torch.Tensor): 标签张量
        batch_first (bool): 是否将批次维度放在第一位

    Returns:
        tuple: (inputs, labels) 调整后的输入和标签
    """
    if not batch_first:
        # 如果 batch_first 为 False，则需要调整维度顺序
        inputs = inputs.permute(1, 0, 2)  # (seq_len, batch_size, input_size)
        labels = labels.permute(1, 0, 2)  # (seq_len, batch_size, input_size)

    return inputs, labels
