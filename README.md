# 通用LSTM时间序列预测模型

这是一个高度通用的LSTM模型，用于时间序列预测。通过调整配置文件中的参数，该模型可以用于各种类型数据的LSTM预测，使得实验和调优变得简单。LSTM通过引入记忆单元和门控机制，解决了RNN的长期依赖问题。自问世以来，它已被广泛应用于自然语言处理和时间序列预测等领域。

## 项目结构

项目采用模块化设计，将数据处理、模型训练和预测等功能解耦分开，使代码结构更加清晰和易于维护。

```
LSTM/
│
├── configs/                  # 配置文件目录
│   └── config.yaml           # 主配置文件
│
├── data/                     # 数据处理相关代码
│   ├── __init__.py
│   ├── data_processor.py     # 数据处理模块
│   └── data.csv              # 数据文件
│
├── models/                   # 模型定义和相关功能
│   ├── __init__.py
│   └── lstm_model.py         # LSTM模型定义
│
├── trainers/                 # 训练相关代码
│   ├── __init__.py
│   └── trainer.py            # 训练器模块
│
├── utils/                    # 工具函数
│   ├── __init__.py
│   ├── config.py             # 配置工具
│   ├── logger.py             # 日志工具
│   └── visualization.py      # 可视化工具
│
├── scripts/                  # 运行脚本
│   ├── train.py              # 训练脚本
│   └── predict.py            # 预测脚本（包含预测逻辑）
│
├── checkpoint/               # 模型检查点保存目录
│
├── log/                      # 日志保存目录
│
└── main.py                   # 主入口文件
```

## 使用方法

### 1. 配置

编辑 `configs/config.yaml` 文件，设置数据路径、模型参数和训练参数等。主要配置项包括：

- 数据文件路径 (`dataroot`)
- 输入和输出特征的列索引 (`inputs_cols`, `label_cols`)
- 模型结构参数 (`hidden_size`, `num_layers` 等)
- 训练参数 (`batch_size`, `lr`, `epochs` 等)
- 模型和日志保存路径 (`save_model_path`, `log_path`)

### 2. 训练模型

使用主入口文件训练模型：

```bash
python main.py --train
```

或者使用专门的训练脚本：

```bash
python scripts/train.py --config ./configs/config.yaml
```

训练过程中，模型会定期保存到 `checkpoint` 目录，训练日志会保存到 `log` 目录。

### 3. 预测

使用主入口文件进行预测：

```bash
python main.py --predict --epoch 200
```

或者使用专门的预测脚本：

```bash
python scripts/predict.py --config ./configs/config.yaml --epoch 200
```

如果要预测未来的数据点，可以添加 `--future` 参数：

```bash
python scripts/predict.py --config ./configs/config.yaml --epoch 200 --future
```

预测结果会显示在控制台，并且会生成可视化图表。

## 配置参数说明

配置文件 `configs/config.yaml` 中的参数分为以下几个部分：

### 数据配置
- `dataroot`: 数据文件路径，默认为 `'./data/data.csv'`
- `inputs_cols`: 输入特征的列索引，例如 `[0, 1]` 表示使用第1列和第2列作为输入
- `label_cols`: 标签/目标值的列索引，例如 `[0, 1]` 表示预测第1列和第2列
- `split_rate`: 训练集与测试集分割比例，默认为 `0.8`（80%用于训练）
- `predict_data`: 预测时使用的数据点数量，默认为 `3`

### LSTM模型结构配置
- `input_size`: 输入特征维度，通常等于 `len(inputs_cols)`
- `hidden_size`: LSTM隐藏层大小，影响模型的容量和表达能力
- `num_layers`: LSTM层数，更多的层可以学习更复杂的模式
- `output_size`: 输出维度，通常等于 `len(label_cols)`
- `dropout_rate`: Dropout比率，用于防止过拟合
- `batch_first`: 是否将批次维度放在第一位，默认为 `True`
- `seq_len`: 序列长度，表示使用前 `seq_len` 个时间步的数据来预测未来

### 训练配置
- `batch_size`: 训练时的批次大小
- `lr`: 学习率，控制模型参数更新的步长
- `epochs`: 训练的轮数

### 学习率调度器配置
- `step_size`: 学习率调整的步长，每隔 `step_size` 个epoch调整一次学习率
- `gamma`: 学习率的衰减因子，每次调整时将学习率乘以 `gamma`

### 路径配置
- `save_model_path`: 模型保存路径，默认为 `"./checkpoint"`
- `log_path`: 日志保存路径，默认为 `"./log"`

## 实验结果

本模型使用的数据是模拟数据，表示随时间变化的船舶受力和力矩。预测值与真实值之间的关系如下图所示：

![af](./figure/af.png)
![am](./figure/am.png)
