"""
训练模块，用于模型训练和评估
"""
import torch
import torch.nn as nn
import os
import numpy as np
from models.lstm_model import save_model
from data.data_processor import adjust_batch_first
from utils.logger import setup_logger
from utils.config import ensure_directory_exists

class LSTMTrainer:
    """
    LSTM模型训练器
    """
    def __init__(self, model, config, device):
        """
        初始化训练器

        Args:
            model (nn.Module): 要训练的模型
            config (dict): 配置字典
            device (str): 设备，'cuda'或'cpu'
        """
        self.model = model
        self.config = config
        self.device = device
        self.weight_decay = config.get("weight_decay", 0)

        # 设置优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=config["lr"],
            weight_decay=self.weight_decay
        )

        # 设置学习率调度器
        self.scheduler = torch.optim.lr_scheduler.StepLR(
            self.optimizer,
            step_size=config["step_size"],
            gamma=config["gamma"]
        )

        # 设置损失函数
        self.loss_fn = nn.MSELoss()

        # 设置日志记录器
        folder_name = f"training_epochs_{config['epochs']}_stepsize_{config['step_size']}_gamma_{config['gamma']}"
        log_path = os.path.join(config["log_path"], folder_name)
        self.logger = setup_logger(log_path)

        # 保存训练和测试损失
        self.train_losses = []
        self.test_losses = []

    def train_epoch(self, train_dataloader):
        """
        训练一个epoch

        Args:
            train_dataloader (DataLoader): 训练数据加载器

        Returns:
            float: 平均训练损失
        """
        self.model.train()
        total_loss = 0
        total_samples = 0

        # 每个batch使用新的隐藏状态，不在batch之间传递
        for x, y in train_dataloader:
            # 调整批次维度
            x, y = adjust_batch_first(x, y, self.config["batch_first"])

            # 将数据移动到设备
            x, y = x.to(self.device), y.to(self.device)

            # 为每个batch创建新的隐藏状态
            batch_size = x.size(0)
            hidden_size = self.model.hidden_size
            num_layers = self.model.num_layers

            # 检查是否是双向LSTM模型
            from models.lstm_model import BiLSTMModel
            if isinstance(self.model, BiLSTMModel):
                # 双向LSTM的隐藏状态维度是num_layers*2
                h_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(self.device)
                c_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(self.device)
            else:
                # 单向LSTM的隐藏状态维度是num_layers
                h_n = torch.zeros(num_layers, batch_size, hidden_size).to(self.device)
                c_n = torch.zeros(num_layers, batch_size, hidden_size).to(self.device)

            # 前向传播
            y_pred, _ = self.model(x, h_n, c_n)

            # 计算损失
            loss = self.loss_fn(y_pred, y)

            # 反向传播和优化
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

            # 累计损失
            total_samples += 1
            with torch.no_grad():
                total_loss += loss.item()

        # 更新学习率
        self.scheduler.step()

        # 计算平均损失
        epoch_loss = total_loss / total_samples
        self.train_losses.append(epoch_loss)

        return epoch_loss

    def evaluate(self, test_dataloader):
        """
        评估模型

        Args:
            test_dataloader (DataLoader): 测试数据加载器

        Returns:
            tuple: (epoch_test_loss, all_y_pred, all_y_true) 平均测试损失和预测结果、真实值
        """
        self.model.eval()
        total_loss = 0
        total_samples = 0
        all_y_pred = []
        all_y_true = []

        with torch.no_grad():
            for x, y in test_dataloader:
                # 调整批次维度
                x, y = adjust_batch_first(x, y, self.config["batch_first"])

                # 将数据移动到设备
                x, y = x.to(self.device), y.to(self.device)

                # 为每个batch创建新的隐藏状态
                batch_size = x.size(0)
                hidden_size = self.model.hidden_size
                num_layers = self.model.num_layers

                # 检查是否是双向LSTM模型
                from models.lstm_model import BiLSTMModel
                if isinstance(self.model, BiLSTMModel):
                    # 双向LSTM的隐藏状态维度是num_layers*2
                    h_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(self.device)
                    c_n = torch.zeros(num_layers * 2, batch_size, hidden_size).to(self.device)
                else:
                    # 单向LSTM的隐藏状态维度是num_layers
                    h_n = torch.zeros(num_layers, batch_size, hidden_size).to(self.device)
                    c_n = torch.zeros(num_layers, batch_size, hidden_size).to(self.device)

                # 前向传播
                y_pred, _ = self.model(x, h_n, c_n)

                # 计算损失
                loss = self.loss_fn(y_pred, y)

                # 累计损失
                total_samples += 1
                total_loss += loss.item()

                # 保存预测结果和真实值
                all_y_pred.append(y_pred.cpu())
                all_y_true.append(y.cpu())

        # 计算平均损失
        epoch_test_loss = total_loss / total_samples
        self.test_losses.append(epoch_test_loss)

        # 合并所有批次的预测结果和真实值
        all_y_pred = torch.cat(all_y_pred, dim=0)
        all_y_true = torch.cat(all_y_true, dim=0)

        return epoch_test_loss, all_y_pred, all_y_true

    def train(self, train_dataloader, test_dataloader, save_interval=10):
        """
        训练模型

        Args:
            train_dataloader (DataLoader): 训练数据加载器
            test_dataloader (DataLoader): 测试数据加载器
            save_interval (int, optional): 保存模型的间隔轮数

        Returns:
            tuple: (all_y_pred, all_y_true) 最终的预测结果和真实值
        """
        epochs = self.config["epochs"]

        # 初始化最佳模型跟踪
        best_test_loss = float('inf')
        best_model_state = None
        best_epoch = 0

        # 早停机制参数
        early_stopping_enabled = self.config.get("early_stopping", False)
        patience = self.config.get("patience", 20)
        no_improvement_count = 0

        # 确保保存目录存在
        folder_name = f"training_epochs_{self.config['epochs']}_stepsize_{self.config['step_size']}_gamma_{self.config['gamma']}"
        save_model_path = os.path.join(self.config["save_model_path"], folder_name)
        ensure_directory_exists(save_model_path)

        for epoch in range(1, epochs + 1):
            # 训练一个epoch
            train_loss = self.train_epoch(train_dataloader)

            # 评估模型
            test_loss, all_y_pred, all_y_true = self.evaluate(test_dataloader)

            # 打印和记录损失
            print(f"Epoch: {epoch}/{epochs}, Train Loss: {train_loss:.6f}, Test Loss: {test_loss:.6f}")
            self.logger.info(f"Epoch {epoch}/{epochs} | Train Loss: {train_loss:.6f} | Test Loss: {test_loss:.6f}")

            # 保存模型
            if epoch % save_interval == 0:
                # 确定模型类型
                model_type = self.config.get("model_type", "lstm").lower()
                save_model(self.model, self.config, epoch, model_type=model_type)

            # 保存最佳模型
            if test_loss < best_test_loss:
                best_test_loss = test_loss
                best_model_state = self.model.state_dict().copy()
                best_epoch = epoch
                print(f"New best model at epoch {epoch} with test loss: {best_test_loss:.6f}")
                self.logger.info(f"New best model at epoch {epoch} with test loss: {best_test_loss:.6f}")

                # 确定模型类型
                model_type = self.config.get("model_type", "lstm").lower()

                # 保存最佳模型
                best_model_path = os.path.join(save_model_path, f"{model_type}_best_model.pth")
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': best_model_state,
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'test_loss': best_test_loss,
                    'model_type': model_type
                }, best_model_path)

                # 重置早停计数器
                no_improvement_count = 0
            else:
                # 增加早停计数器
                no_improvement_count += 1

            # 早停检查
            if early_stopping_enabled and no_improvement_count >= patience:
                print(f"Early stopping triggered after {epoch} epochs. No improvement for {patience} consecutive epochs.")
                self.logger.info(f"Early stopping triggered after {epoch} epochs. No improvement for {patience} consecutive epochs.")
                break

        # 训练结束后，恢复最佳模型
        print(f"Training completed. Best model was at epoch {best_epoch} with test loss: {best_test_loss:.6f}")
        self.logger.info(f"Training completed. Best model was at epoch {best_epoch} with test loss: {best_test_loss:.6f}")

        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        return all_y_pred, all_y_true
