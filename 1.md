# 深度学习复习大纲：张量与神经网络基础

---

## 一、张量的创建与变换

### 1. 张量的定义与基本操作
- **张量**：多维数组（标量、向量、矩阵、高阶张量）
- **创建方式**：
  - 直接创建（如 `torch.tensor([[1,2],[3,4]]`)）
  - 特殊张量（全零、全一、随机、单位矩阵）
- **变换操作**：
  - 形状变换：`reshape`, `view`, `squeeze/unsqueeze`
  - 数据类型转换：`float32` → `int64`
  - 数学运算：加减乘除、矩阵乘法
- **内存共享**：`view()` 与原张量共享内存

---

## 二、前馈神经网络（FNN）

### 1. 神经元工作机制
- **基本工作流程**：
  1. **加权求和**：输入向量与权重的线性组合（$z = w_1x_1 + w_2x_2 + \dots + b$）
  2. **激活函数**：对线性结果进行非线性变换（$a = \sigma(z)$）
  3. **输出传递**：将激活后的结果传递给下一层神经元
- **对于当前神经元来说**：
  - 先进行加权求和计算
  - 然后通过激活函数进行非线性变换
  - 这种非线性变换是神经网络学习复杂模式的关键

### 2. 常见激活函数
- **Sigmoid**：
  $\sigma(z) = \frac{1}{1+e^{-z}}$（输出 $0\sim1$，存在梯度消失问题）
- **ReLU**：
  $f(z) = \max(0, z)$（缓解梯度消失，计算高效）
- **Tanh**：
  $\tanh(z) = \frac{e^z - e^{-z}}{e^z + e^{-z}}$（输出 $-1\sim1$，中心化）
- **Softmax**：多分类概率归一化

### 3. 简单前馈神经网络模型
- **示例结构**：
  输入层（3节点）→ 隐藏层（2节点，ReLU）→ 输出层（1节点，Sigmoid）
- **前向计算过程**：
  1. 输入层 → 隐藏层：$h = \text{ReLU}(W_1 X + b_1)$
  2. 隐藏层 → 输出层：$y = \text{Sigmoid}(W_2 h + b_2)$

### 4. 前向传播与反向调参
- **前向传播（Forward Propagation）**：
  - 数据从输入层开始，逐层向前传递
  - 每一层都进行加权求和和激活函数变换
  - 最终得到网络的预测输出
  - 计算过程：输入 → 隐藏层1 → 隐藏层2 → ... → 输出层

- **反向传播（Backpropagation）**：
  - 计算损失函数对网络参数的梯度
  - 使用链式法则从输出层向输入层逐层传播梯度
  - 更新权重和偏置参数以最小化损失
  - **反向调参过程**：
    1. 计算输出层误差
    2. 反向传播误差到隐藏层
    3. 计算每层参数的梯度
    4. 使用优化器（SGD、Adam等）更新参数

---

## 三、卷积神经网络（CNN）

### 1. 卷积层工作原理
- **核心概念**：
  - 卷积核（滤波器）：提取局部特征（如边缘、纹理）
  - 步长（Stride）：滑动窗口的移动步幅
  - 填充（Padding）：保持特征图尺寸（如 `same padding`）
- **输出尺寸公式**：
  $H_{\text{out}} = \frac{H_{\text{in}} + 2P - K}{S} + 1$（$K$ 为核尺寸，$S$ 为步长）

### 2. 池化层（Pooling）
- **作用**：降维、平移不变性、防止过拟合
- **类型**：
  - 最大池化：保留局部最大值
  - 平均池化：计算局部区域均值

### 3. 经典网络结构
- **LeNet-5**：
  - 早期 CNN 结构：卷积层 + 池化层交替 → 全连接层
  - 应用：手写数字识别（MNIST）
- **VGGNet**：
  - 深层网络（16~19层），全部使用 $3\times3$ 卷积核
  - 模块化堆叠结构，增强特征表达能力
- **InceptionNet（GoogLeNet）**：
  - **Inception 模块**：并行多尺度卷积（$1\times1$、$3\times3$、$5\times5$、池化）
  - $1\times1$ 卷积降维，减少计算量

### 4. 语义分割
- **核心思想**：像素级分类（如区分图像中的物体）
- **全卷积网络（FCN）**：
  用卷积层替代全连接层，保留空间信息
- **U-Net**：
  编码-解码结构，跳跃连接融合浅层细节与深层语义

---

## 复习重点

### 核心概念掌握
1. **张量操作**：创建、形状变换、内存共享机制
2. **神经元工作机制**：加权求和 → 激活函数 → 非线性变换的完整流程
3. **激活函数对比**：Sigmoid vs ReLU vs Tanh 的特点和适用场景
4. **前馈神经网络**：能够手工计算简单网络的前向传播过程
5. **前向传播与反向调参**：理解梯度计算和参数更新的原理

### CNN重点内容
6. **卷积层工作原理**：卷积核滑动、特征提取、参数共享机制
7. **池化工作原理**：最大池化和平均池化的作用和计算方式
8. **经典网络架构差异**：
   - **LeNet**：早期CNN结构，卷积+池化的基本模式
   - **VGGNet**：深层网络，统一使用3×3卷积核的设计思想
   - **InceptionNet**：多分支并行结构，1×1卷积降维技术
9. **语义分割**：像素级分类任务，FCN与U-Net的核心改进

### 实践能力要求
10. **能够给出简单前馈神经网络模型并从输入计算输出**
11. **理解对于当前神经元的计算过程**：先加权求和，通过激活函数进行非线性变换
12. **掌握卷积和池化的数学计算过程**

---

**建议**：
结合代码实践（如 PyTorch/TensorFlow）理解张量操作和网络结构，通过绘制网络图辅助记忆经典模型。