"""
LSTM模型模块，定义LSTM网络结构和相关功能
包含单向LSTM和双向LSTM两种模型
"""
import torch
import torch.nn as nn
import os
from utils.config import ensure_directory_exists

class LSTMModel(nn.Module):
    """
    LSTM模型类
    """
    def __init__(self, config):
        """
        初始化LSTM模型

        Args:
            config (dict): 配置字典，包含模型参数
        """
        super(LSTMModel, self).__init__()

        # 从配置中获取参数
        self.input_size = config["input_size"]
        self.hidden_size = config["hidden_size"]
        self.num_layers = config["num_layers"]
        self.output_size = config["output_size"]
        self.batch_first = config["batch_first"]
        self.dropout_rate = config["dropout_rate"]
        self.predict_data = config["predict_data"]

        # 定义LSTM层
        self.lstm = nn.LSTM(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            batch_first=self.batch_first,
            dropout=self.dropout_rate
        )

        # 添加批量归一化层
        self.bn = nn.BatchNorm1d(self.hidden_size)

        # 定义全连接层
        self.linear = nn.Linear(
            in_features=self.hidden_size,
            out_features=self.output_size
        )

    def forward(self, x, h_n=None, c_n=None):
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量
            h_n (torch.Tensor, optional): 隐藏状态
            c_n (torch.Tensor, optional): 细胞状态

        Returns:
            tuple: (output, (h_n, c_n)) 输出和更新后的隐藏状态、细胞状态
        """
        # 如果没有提供隐藏状态和细胞状态，则初始化为零
        if h_n is None or c_n is None:
            batch_size = x.size(0) if self.batch_first else x.size(1)
            h_n = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
            c_n = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)

        # LSTM前向传播
        output, (h_n, c_n) = self.lstm(x, (h_n, c_n))

        # 提取最后几个时间步的隐藏状态
        if self.batch_first:
            last_hidden = output[:, -self.predict_data:, :]  # (batch_size, predict_data, hidden_size)

            # 应用批量归一化 - 需要调整维度以适应BatchNorm1d
            batch_size, predict_data, hidden_size = last_hidden.shape
            last_hidden_reshaped = last_hidden.reshape(-1, hidden_size)  # (batch_size * predict_data, hidden_size)
            normalized = self.bn(last_hidden_reshaped)  # 应用批量归一化
            normalized = normalized.reshape(batch_size, predict_data, hidden_size)  # 恢复原始形状

            # 通过全连接层
            output = self.linear(normalized)  # (batch_size, predict_data, output_size)
        else:
            last_hidden = output[-self.predict_data:, :, :]  # (predict_data, batch_size, hidden_size)

            # 应用批量归一化 - 需要调整维度
            predict_data, batch_size, hidden_size = last_hidden.shape
            last_hidden_reshaped = last_hidden.permute(1, 0, 2).reshape(-1, hidden_size)  # (batch_size * predict_data, hidden_size)
            normalized = self.bn(last_hidden_reshaped)  # 应用批量归一化
            normalized = normalized.reshape(batch_size, predict_data, hidden_size).permute(1, 0, 2)  # 恢复原始形状

            # 通过全连接层
            output = self.linear(normalized)  # (predict_data, batch_size, output_size)
            output = output.permute(1, 0, 2)  # (batch_size, predict_data, output_size)

        return output, (h_n, c_n)


def save_model(model, config, epoch, folder_name=None, model_type=None):
    """
    保存模型权重

    Args:
        model (nn.Module): 要保存的模型
        config (dict): 配置字典
        epoch (int): 当前训练轮数
        folder_name (str, optional): 保存文件夹名称，如果为None则使用默认名称
        model_type (str, optional): 模型类型，用于日志输出
    """
    # 如果没有提供文件夹名称，则使用默认名称
    if folder_name is None:
        folder_name = f"training_epochs_{config['epochs']}_stepsize_{config['step_size']}_gamma_{config['gamma']}"

    # 确定模型类型
    if model_type is None:
        if isinstance(model, BiLSTMModel):
            model_type = "BiLSTM"
        else:
            model_type = "LSTM"

    # 构建保存路径
    save_dir = os.path.join(config["save_model_path"], folder_name)
    ensure_directory_exists(save_dir)

    # 构建文件名
    filename = f"{model_type.lower()}_model_epoch{epoch}.pth"
    save_path = os.path.join(save_dir, filename)

    # 保存模型
    torch.save(model.state_dict(), save_path)
    print(f"{model_type} model for epoch {epoch} saved at {save_path}!")

    return save_path


class BiLSTMModel(nn.Module):
    """
    双向LSTM模型类，增强版本，添加了额外的正则化技术
    """
    def __init__(self, config):
        """
        初始化双向LSTM模型

        Args:
            config (dict): 配置字典，包含模型参数
        """
        super(BiLSTMModel, self).__init__()

        # 从配置中获取参数
        self.input_size = config["input_size"]
        self.hidden_size = config["hidden_size"]
        self.num_layers = config["num_layers"]
        self.output_size = config["output_size"]
        self.batch_first = config["batch_first"]
        self.dropout_rate = config["dropout_rate"]
        self.predict_data = config["predict_data"]

        # 定义双向LSTM层
        self.lstm = nn.LSTM(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            batch_first=self.batch_first,
            dropout=self.dropout_rate,
            bidirectional=True  # 设置为双向LSTM
        )

        # 添加批量归一化层 - 注意双向LSTM的隐藏状态维度是hidden_size*2
        self.bn = nn.BatchNorm1d(self.hidden_size * 2)

        # 添加额外的Dropout层，用于输入和输出之间
        self.dropout = nn.Dropout(self.dropout_rate)

        # 定义全连接层 - 输入维度是hidden_size*2，因为是双向LSTM
        self.linear = nn.Linear(
            in_features=self.hidden_size * 2,
            out_features=self.output_size
        )

        # 初始化权重，使用Xavier初始化
        self.init_weights()

    def init_weights(self):
        """
        初始化模型权重，使用Xavier初始化
        """
        for name, param in self.named_parameters():
            if 'weight_ih' in name or 'weight_hh' in name:
                # LSTM权重使用Xavier初始化
                nn.init.xavier_uniform_(param)
            elif 'bias' in name:
                # 偏置初始化为0
                nn.init.zeros_(param)
            elif 'weight' in name and param.dim() > 1:
                # 线性层权重使用Xavier初始化
                nn.init.xavier_uniform_(param)

    def forward(self, x, h_n=None, c_n=None):
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量
            h_n (torch.Tensor, optional): 隐藏状态
            c_n (torch.Tensor, optional): 细胞状态

        Returns:
            tuple: (output, (h_n, c_n)) 输出和更新后的隐藏状态、细胞状态
        """
        # 如果没有提供隐藏状态和细胞状态，则初始化为零
        if h_n is None or c_n is None:
            batch_size = x.size(0) if self.batch_first else x.size(1)
            # 注意双向LSTM的隐藏状态维度是num_layers*2
            h_n = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
            c_n = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)

        # LSTM前向传播
        output, (h_n, c_n) = self.lstm(x, (h_n, c_n))

        # 提取最后几个时间步的隐藏状态
        if self.batch_first:
            last_hidden = output[:, -self.predict_data:, :]  # (batch_size, predict_data, hidden_size*2)

            # 应用批量归一化 - 需要调整维度以适应BatchNorm1d
            batch_size, predict_data, hidden_size = last_hidden.shape
            last_hidden_reshaped = last_hidden.reshape(-1, hidden_size)  # (batch_size * predict_data, hidden_size*2)
            normalized = self.bn(last_hidden_reshaped)  # 应用批量归一化
            normalized = normalized.reshape(batch_size, predict_data, hidden_size)  # 恢复原始形状

            # 应用dropout
            normalized = self.dropout(normalized)

            # 通过全连接层
            output = self.linear(normalized)  # (batch_size, predict_data, output_size)
        else:
            last_hidden = output[-self.predict_data:, :, :]  # (predict_data, batch_size, hidden_size*2)

            # 应用批量归一化 - 需要调整维度
            predict_data, batch_size, hidden_size = last_hidden.shape
            last_hidden_reshaped = last_hidden.permute(1, 0, 2).reshape(-1, hidden_size)  # (batch_size * predict_data, hidden_size*2)
            normalized = self.bn(last_hidden_reshaped)  # 应用批量归一化
            normalized = normalized.reshape(batch_size, predict_data, hidden_size).permute(1, 0, 2)  # 恢复原始形状

            # 应用dropout - 需要调整维度
            normalized = normalized.permute(1, 0, 2)  # (batch_size, predict_data, hidden_size)
            normalized = self.dropout(normalized)
            normalized = normalized.permute(1, 0, 2)  # (predict_data, batch_size, hidden_size)

            # 通过全连接层
            output = self.linear(normalized)  # (predict_data, batch_size, output_size)
            output = output.permute(1, 0, 2)  # (batch_size, predict_data, output_size)

        return output, (h_n, c_n)


def load_model(config, epoch, device, folder_name=None, model_type="lstm"):
    """
    加载模型权重

    Args:
        config (dict): 配置字典
        epoch (int): 要加载的模型轮数
        device (str): 设备，'cuda'或'cpu'
        folder_name (str, optional): 模型文件夹名称，如果为None则使用默认名称
        model_type (str, optional): 模型类型，"lstm"或"bilstm"

    Returns:
        nn.Module: 加载了权重的模型
    """
    # 如果没有提供文件夹名称，则使用默认名称
    if folder_name is None:
        folder_name = f"training_epochs_{config['epochs']}_stepsize_{config['step_size']}_gamma_{config['gamma']}"

    # 构建加载路径
    filename = f"model_epoch{epoch}.pth"
    load_dir = os.path.join(config["save_model_path"], folder_name)
    load_path = os.path.join(load_dir, filename)

    # 创建模型实例
    if model_type.lower() == "bilstm":
        model = BiLSTMModel(config).to(device)
    else:
        model = LSTMModel(config).to(device)

    # 加载模型权重
    model.load_state_dict(torch.load(load_path, map_location=device))
    print(f"{model_type.upper()} model loaded from {load_path}!")

    return model
