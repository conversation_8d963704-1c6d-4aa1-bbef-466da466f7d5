# 数据配置 (Data Configuration)
dataroot: './data/data.csv'           # 数据文件路径 (Path to the input data CSV file)
inputs_cols: [0, 1]              # 输入特征的列索引 (Column indices for input features)
label_cols: [0, 1]               # 标签/目标值的列索引 (Column indices for labels/target values)
split_rate: 0.8                  # 训练集与测试集分割比例 (Train-test split ratio)
predict_data: 3                  # 预测时使用的数据点数量 (Number of data points to use for prediction)

# LSTM模型结构配置 (LSTM Model Structure Configuration)
input_size: 2                    # 输入特征维度 (Dimension of input features)
hidden_size: 48                  # LSTM隐藏层大小 (Size of LSTM hidden layer)
num_layers: 2                    # LSTM层数 (Number of LSTM layers)
output_size: 2                   # 输出维度 (Dimension of output)
dropout_rate: 0                  # Dropout比率，用于防止过拟合 (Dropout rate to prevent overfitting)
batch_first: True                # 是否将批次维度放在第一位 (Whether batch dimension is the first dimension)
seq_len: 20                      # 序列长度 (Sequence length for LSTM input)

# 训练配置 (Training Configuration)
batch_size: 2                    # 批次大小 (Batch size for training)
lr: 0.001                        # 学习率 (Learning rate)
epochs: 200                      # 训练轮数 (Number of training epochs)

# 学习率调度器配置 (Learning Rate Scheduler Configuration)
step_size: 7                     # 学习率调整步长 (Step size for learning rate adjustment)
gamma: 0.75                      # 学习率衰减因子 (Learning rate decay factor)
weight_decay: 0.00001

# 路径配置 (Path Configuration)
save_model_path: "./checkpoint"  # 模型保存路径 (Path to save model checkpoints)
log_path: "./log"                # 日志保存路径 (Path to save logs)
