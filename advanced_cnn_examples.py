"""
高级CNN网络实现
包含VGGNet、InceptionNet和语义分割网络的实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
import numpy as np

# ============================================================================
# VGGNet实现
# ============================================================================

class VGGBlock(nn.Module):
    """VGG基本块"""
    def __init__(self, in_channels, out_channels, num_convs):
        super(VGGBlock, self).__init__()
        layers = []
        for i in range(num_convs):
            layers.append(nn.Conv2d(in_channels if i == 0 else out_channels, 
                                  out_channels, kernel_size=3, padding=1))
            layers.append(nn.ReLU(inplace=True))
        layers.append(nn.MaxPool2d(kernel_size=2, stride=2))
        self.block = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.block(x)

class VGG16(nn.Module):
    """VGG16网络实现"""
    def __init__(self, num_classes=1000):
        super(VGG16, self).__init__()
        
        # VGG16的配置：[64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 512, 512, 512, 'M', 512, 512, 512, 'M']
        self.features = nn.Sequential(
            # Block 1
            VGGBlock(3, 64, 2),    # 2个3x3卷积 + MaxPool
            # Block 2  
            VGGBlock(64, 128, 2),  # 2个3x3卷积 + MaxPool
            # Block 3
            VGGBlock(128, 256, 3), # 3个3x3卷积 + MaxPool
            # Block 4
            VGGBlock(256, 512, 3), # 3个3x3卷积 + MaxPool
            # Block 5
            VGGBlock(512, 512, 3), # 3个3x3卷积 + MaxPool
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(),
            nn.Linear(4096, num_classes)
        )
    
    def forward(self, x):
        print(f"输入: {x.shape}")
        
        x = self.features(x)
        print(f"特征提取后: {x.shape}")
        
        x = x.view(x.size(0), -1)
        print(f"展平后: {x.shape}")
        
        x = self.classifier(x)
        print(f"分类器输出: {x.shape}")
        
        return x

def vgg_demo():
    """VGG网络演示"""
    print("=" * 50)
    print("VGG16网络演示")
    print("=" * 50)
    
    # 创建VGG16网络
    model = VGG16(num_classes=10)
    
    # 创建输入 (batch_size=1, channels=3, height=224, width=224)
    x = torch.randn(1, 3, 224, 224)
    
    # 前向传播
    output = model(x)
    
    # 统计参数数量
    total_params = sum(p.numel() for p in model.parameters())
    print(f"VGG16总参数数量: {total_params:,}")

# ============================================================================
# Inception模块实现
# ============================================================================

class InceptionModule(nn.Module):
    """Inception模块实现"""
    def __init__(self, in_channels, ch1x1, ch3x3red, ch3x3, ch5x5red, ch5x5, pool_proj):
        super(InceptionModule, self).__init__()
        
        # 1x1卷积分支
        self.branch1 = nn.Conv2d(in_channels, ch1x1, kernel_size=1)
        
        # 1x1卷积 + 3x3卷积分支
        self.branch2 = nn.Sequential(
            nn.Conv2d(in_channels, ch3x3red, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(ch3x3red, ch3x3, kernel_size=3, padding=1)
        )
        
        # 1x1卷积 + 5x5卷积分支
        self.branch3 = nn.Sequential(
            nn.Conv2d(in_channels, ch5x5red, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(ch5x5red, ch5x5, kernel_size=5, padding=2)
        )
        
        # 3x3最大池化 + 1x1卷积分支
        self.branch4 = nn.Sequential(
            nn.MaxPool2d(kernel_size=3, stride=1, padding=1),
            nn.Conv2d(in_channels, pool_proj, kernel_size=1)
        )
    
    def forward(self, x):
        print(f"Inception输入: {x.shape}")
        
        branch1 = F.relu(self.branch1(x))
        print(f"1x1分支: {branch1.shape}")
        
        branch2 = F.relu(self.branch2(x))
        print(f"3x3分支: {branch2.shape}")
        
        branch3 = F.relu(self.branch3(x))
        print(f"5x5分支: {branch3.shape}")
        
        branch4 = F.relu(self.branch4(x))
        print(f"池化分支: {branch4.shape}")
        
        # 在通道维度上拼接
        outputs = torch.cat([branch1, branch2, branch3, branch4], dim=1)
        print(f"拼接后: {outputs.shape}")
        
        return outputs

def inception_demo():
    """Inception模块演示"""
    print("\n" + "=" * 50)
    print("Inception模块演示")
    print("=" * 50)
    
    # 创建Inception模块
    # 参数：输入通道, 1x1, 3x3reduce, 3x3, 5x5reduce, 5x5, pool_proj
    inception = InceptionModule(192, 64, 96, 128, 16, 32, 32)
    
    # 创建输入
    x = torch.randn(1, 192, 28, 28)
    
    # 前向传播
    output = inception(x)

# ============================================================================
# 语义分割网络实现
# ============================================================================

class FCN(nn.Module):
    """全卷积网络(FCN)实现"""
    def __init__(self, num_classes):
        super(FCN, self).__init__()
        
        # 编码器部分（特征提取）
        self.encoder = nn.Sequential(
            # Block 1
            nn.Conv2d(3, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, stride=2),
            
            # Block 2
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, stride=2),
            
            # Block 3
            nn.Conv2d(128, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, stride=2),
        )
        
        # 分类器（用卷积替代全连接）
        self.classifier = nn.Sequential(
            nn.Conv2d(256, 512, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Dropout2d(),
            nn.Conv2d(512, 512, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Dropout2d(),
            nn.Conv2d(512, num_classes, 1)  # 1x1卷积输出分类结果
        )
        
        # 上采样层
        self.upsample = nn.ConvTranspose2d(num_classes, num_classes, 8, stride=8)
    
    def forward(self, x):
        print(f"FCN输入: {x.shape}")
        
        # 编码器
        features = self.encoder(x)
        print(f"特征提取: {features.shape}")
        
        # 分类器
        classified = self.classifier(features)
        print(f"分类结果: {classified.shape}")
        
        # 上采样到原始尺寸
        output = self.upsample(classified)
        print(f"上采样后: {output.shape}")
        
        return output

class UNet(nn.Module):
    """U-Net网络实现"""
    def __init__(self, num_classes):
        super(UNet, self).__init__()
        
        # 编码器
        self.enc1 = self.conv_block(3, 64)
        self.enc2 = self.conv_block(64, 128)
        self.enc3 = self.conv_block(128, 256)
        self.enc4 = self.conv_block(256, 512)
        
        # 瓶颈层
        self.bottleneck = self.conv_block(512, 1024)
        
        # 解码器
        self.upconv4 = nn.ConvTranspose2d(1024, 512, 2, stride=2)
        self.dec4 = self.conv_block(1024, 512)
        
        self.upconv3 = nn.ConvTranspose2d(512, 256, 2, stride=2)
        self.dec3 = self.conv_block(512, 256)
        
        self.upconv2 = nn.ConvTranspose2d(256, 128, 2, stride=2)
        self.dec2 = self.conv_block(256, 128)
        
        self.upconv1 = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.dec1 = self.conv_block(128, 64)
        
        # 输出层
        self.final_conv = nn.Conv2d(64, num_classes, 1)
    
    def conv_block(self, in_channels, out_channels):
        """卷积块"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        print(f"U-Net输入: {x.shape}")
        
        # 编码器路径
        enc1 = self.enc1(x)
        print(f"编码器1: {enc1.shape}")
        
        enc2 = self.enc2(F.max_pool2d(enc1, 2))
        print(f"编码器2: {enc2.shape}")
        
        enc3 = self.enc3(F.max_pool2d(enc2, 2))
        print(f"编码器3: {enc3.shape}")
        
        enc4 = self.enc4(F.max_pool2d(enc3, 2))
        print(f"编码器4: {enc4.shape}")
        
        # 瓶颈层
        bottleneck = self.bottleneck(F.max_pool2d(enc4, 2))
        print(f"瓶颈层: {bottleneck.shape}")
        
        # 解码器路径（带跳跃连接）
        dec4 = self.upconv4(bottleneck)
        dec4 = torch.cat([dec4, enc4], dim=1)  # 跳跃连接
        dec4 = self.dec4(dec4)
        print(f"解码器4: {dec4.shape}")
        
        dec3 = self.upconv3(dec4)
        dec3 = torch.cat([dec3, enc3], dim=1)
        dec3 = self.dec3(dec3)
        print(f"解码器3: {dec3.shape}")
        
        dec2 = self.upconv2(dec3)
        dec2 = torch.cat([dec2, enc2], dim=1)
        dec2 = self.dec2(dec2)
        print(f"解码器2: {dec2.shape}")
        
        dec1 = self.upconv1(dec2)
        dec1 = torch.cat([dec1, enc1], dim=1)
        dec1 = self.dec1(dec1)
        print(f"解码器1: {dec1.shape}")
        
        # 输出
        output = self.final_conv(dec1)
        print(f"最终输出: {output.shape}")
        
        return output

def segmentation_demo():
    """语义分割网络演示"""
    print("\n" + "=" * 50)
    print("语义分割网络演示")
    print("=" * 50)
    
    # FCN演示
    print("FCN网络:")
    fcn = FCN(num_classes=21)  # PASCAL VOC有21个类别
    x = torch.randn(1, 3, 224, 224)
    fcn_output = fcn(x)
    
    print("\nU-Net网络:")
    unet = UNet(num_classes=21)
    x = torch.randn(1, 3, 256, 256)
    unet_output = unet(x)

# ============================================================================
# 主函数
# ============================================================================

def main():
    """运行所有高级CNN演示"""
    print("高级CNN网络演示")
    print("=" * 80)
    
    # 1. VGG网络
    vgg_demo()
    
    # 2. Inception模块
    inception_demo()
    
    # 3. 语义分割网络
    segmentation_demo()
    
    print("\n" + "=" * 80)
    print("高级CNN演示完成！")

if __name__ == "__main__":
    main()
