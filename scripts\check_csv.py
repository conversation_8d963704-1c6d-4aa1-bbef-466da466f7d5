"""
检查CSV文件的内容和统计信息
"""
import pandas as pd
import numpy as np
import sys

def check_csv(csv_path):
    """
    检查CSV文件的内容和统计信息
    
    Args:
        csv_path (str): CSV文件路径
    """
    # 读取CSV文件
    print(f"正在读取CSV文件: {csv_path}")
    df = pd.read_csv(csv_path)
    
    # 基本信息
    print(f"\n数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    
    # 数据类型
    print("\n数据类型:")
    print(df.dtypes)
    
    # 统计信息
    print("\n统计信息:")
    print(df.describe())
    
    # 缺失值
    missing = df.isnull().sum()
    print("\n缺失值:")
    print(missing[missing > 0] if any(missing > 0) else "无缺失值")
    
    # 前几行数据
    print("\n前5行数据:")
    print(df.head())
    
    # 后几行数据
    print("\n后5行数据:")
    print(df.tail())
    
    # 时间范围（如果有日期列）
    date_cols = [col for col in df.columns if 'date' in str(col).lower() or 'time' in str(col).lower() or '日期' in str(col) or '时间' in str(col)]
    if date_cols:
        print("\n时间范围:")
        for col in date_cols:
            if pd.api.types.is_datetime64_any_dtype(df[col]) or pd.api.types.is_string_dtype(df[col]):
                try:
                    df[col] = pd.to_datetime(df[col])
                    print(f"{col}: {df[col].min()} 到 {df[col].max()}")
                except:
                    pass

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python check_csv.py <csv_path>")
        sys.exit(1)
    
    check_csv(sys.argv[1])
