"""
配置模块，用于加载和处理配置文件
"""
import yaml
import os

def config_loader(path):
    """
    加载YAML配置文件
    
    Args:
        path (str): 配置文件路径
        
    Returns:
        dict: 配置字典
    """
    with open(path, 'r', encoding='utf-8') as stream:
        cfgs = yaml.safe_load(stream)  # 读取 YAML 文件内容并转换为字典
    return cfgs

def ensure_directory_exists(directory_path):
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path (str): 目录路径
    """
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"Created directory: {directory_path}")
