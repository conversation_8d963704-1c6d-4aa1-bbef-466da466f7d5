"""
训练脚本，用于训练LSTM模型
"""
import argparse
import torch
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import config_loader, ensure_directory_exists
from data.data_processor import load_and_process_data
from models.lstm_model import LSTMModel
from trainers.trainer import LSTMTrainer
from utils.visualization import plot_loss_curves, plot_predictions

def main():
    """
    主函数，解析命令行参数并训练模型
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Train LSTM model')
    parser.add_argument('--config', type=str, default='./configs/config.yaml',
                        help='Path to config file')
    args = parser.parse_args()

    # 加载配置
    config = config_loader(args.config)

    # 确保模型保存目录和日志目录存在
    ensure_directory_exists(config["save_model_path"])
    ensure_directory_exists(config["log_path"])

    # 加载数据
    print("Loading and processing data...")
    train_dataloader, test_dataloader, min_val, max_val = load_and_process_data(config)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 创建模型
    print("Creating model...")
    model = LSTMModel(config).to(device)

    # 创建训练器
    print("Creating trainer...")
    trainer = LSTMTrainer(model, config, device)

    # 训练模型
    print("Starting training...")
    all_y_pred, all_y_true = trainer.train(train_dataloader, test_dataloader)

    # 绘制损失曲线
    print("Plotting loss curves...")
    plot_loss_curves(trainer)

    # 绘制预测结果
    print("Plotting predictions...")
    plot_predictions(all_y_pred, all_y_true, min_val, max_val, config)

    print("Training completed!")

if __name__ == '__main__':
    main()
