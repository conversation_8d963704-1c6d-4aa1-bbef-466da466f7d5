"""
将Excel文件转换为CSV格式，并添加时间特征
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime

def excel_to_csv(excel_path, csv_path=None, add_time_features=True):
    """
    将Excel文件转换为CSV格式，并可选择添加时间特征

    Args:
        excel_path (str): Excel文件路径
        csv_path (str, optional): 输出CSV文件路径，如果为None则使用相同文件名
        add_time_features (bool, optional): 是否添加时间特征

    Returns:
        str: 输出CSV文件路径
    """
    # 确定输出路径
    if csv_path is None:
        base_name = os.path.splitext(excel_path)[0]
        csv_path = f"{base_name}.csv"

    # 读取Excel文件
    print(f"正在读取Excel文件: {excel_path}")
    try:
        # 尝试使用pandas读取Excel文件
        df = pd.read_excel(excel_path)
        print(f"成功读取Excel文件，形状: {df.shape}")

        # 显示前几行数据
        print("\n数据预览:")
        print(df.head())

        # 显示列名
        print("\n列名:")
        print(df.columns.tolist())

        # 检查是否有日期/时间列
        date_cols = []
        for col in df.columns:
            if 'date' in str(col).lower() or 'time' in str(col).lower() or '日期' in str(col) or '时间' in str(col):
                date_cols.append(col)

        if date_cols:
            print(f"\n检测到可能的日期/时间列: {date_cols}")

        # 添加时间特征
        if add_time_features and date_cols:
            print("\n添加时间特征...")
            for date_col in date_cols:
                try:
                    # 确保日期列是datetime类型
                    df[date_col] = pd.to_datetime(df[date_col])

                    # 添加年、月、日、小时、分钟、星期几等特征
                    df[f'{date_col}_year'] = df[date_col].dt.year
                    df[f'{date_col}_month'] = df[date_col].dt.month
                    df[f'{date_col}_day'] = df[date_col].dt.day
                    df[f'{date_col}_hour'] = df[date_col].dt.hour
                    df[f'{date_col}_minute'] = df[date_col].dt.minute
                    df[f'{date_col}_dayofweek'] = df[date_col].dt.dayofweek
                    df[f'{date_col}_quarter'] = df[date_col].dt.quarter
                    df[f'{date_col}_dayofyear'] = df[date_col].dt.dayofyear

                    print(f"为列 '{date_col}' 添加了时间特征")
                except Exception as e:
                    print(f"为列 '{date_col}' 添加时间特征时出错: {e}")
        elif add_time_features:
            print("\n未检测到日期/时间列，无法添加时间特征")

        # 处理缺失值
        missing_values = df.isnull().sum().sum()
        if missing_values > 0:
            print(f"\n检测到 {missing_values} 个缺失值，正在填充...")
            # 对数值列使用中位数填充，对非数值列使用众数填充
            for col in df.columns:
                if df[col].dtype.kind in 'ifc':  # 整数、浮点数或复数
                    df[col] = df[col].fillna(df[col].median())
                else:
                    df[col] = df[col].fillna(df[col].mode()[0] if not df[col].mode().empty else "")

        # 保存为CSV
        print(f"\n正在保存为CSV: {csv_path}")
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')  # 使用带BOM的UTF-8编码
        print(f"成功保存CSV文件，形状: {df.shape}")

        return csv_path

    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='将Excel文件转换为CSV格式')
    parser.add_argument('--excel', type=str, required=True,
                        help='Excel文件路径')
    parser.add_argument('--csv', type=str, default=None,
                        help='输出CSV文件路径')
    parser.add_argument('--no-time-features', action='store_true',
                        help='不添加时间特征')
    args = parser.parse_args()

    # 转换Excel到CSV
    excel_to_csv(args.excel, args.csv, not args.no_time_features)

if __name__ == "__main__":
    main()
