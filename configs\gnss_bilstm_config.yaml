# GNSS数据双向LSTM配置文件 - 手动创建于 2025-05-20
# 基于改进配置，使用双向LSTM模型

dataroot: ./data/gnss_all_stations.csv
inputs_cols:
- 7
- 10
- 14
- 18
- 22
- 26
label_cols:
- 10  # △H
split_rate: 0.8
predict_data: 1
input_size: 6
hidden_size: 32       # 隐藏层大小
num_layers: 1         # LSTM层数
output_size: 1
dropout_rate: 0.5     # Dropout率
batch_first: true
seq_len: 10           # 序列长度
batch_size: 32         # 批量大小
lr: 0.002            # 学习率
epochs: 200           # 训练轮数
step_size: 20         # 学习率衰减步长
gamma: 0.3            # 学习率衰减因子
weight_decay: 0.0001 # 权重衰减
save_model_path: ./checkpoint
log_path: ./log
model_type: bilstm    # 使用双向LSTM模型
