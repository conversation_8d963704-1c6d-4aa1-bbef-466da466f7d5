"""
深度学习复习代码示例
包含张量操作、前馈神经网络、卷积神经网络的完整实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt

# ============================================================================
# 一、张量的创建和变换
# ============================================================================

def tensor_operations_demo():
    """张量操作演示"""
    print("=" * 50)
    print("张量操作演示")
    print("=" * 50)
    
    # 1. 张量创建
    print("1. 张量创建:")
    # 从数据创建
    data = [[1, 2], [3, 4]]
    tensor_from_data = torch.tensor(data)
    print(f"从数据创建: {tensor_from_data}")
    
    # 特殊张量
    zeros_tensor = torch.zeros(2, 3)
    ones_tensor = torch.ones(2, 3)
    random_tensor = torch.randn(2, 3)
    print(f"全零张量: {zeros_tensor}")
    print(f"全一张量: {ones_tensor}")
    print(f"随机张量: {random_tensor}")
    
    # 2. 张量变换
    print("\n2. 张量变换:")
    x = torch.randn(2, 3, 4)
    print(f"原始形状: {x.shape}")
    
    # 形状变换
    reshaped = x.reshape(3, 8)
    viewed = x.view(6, 4)
    print(f"reshape后: {reshaped.shape}")
    print(f"view后: {viewed.shape}")
    
    # 转置
    transposed = x.transpose(1, 2)
    print(f"转置后: {transposed.shape}")
    
    # 3. 数学运算
    print("\n3. 数学运算:")
    a = torch.tensor([[1, 2], [3, 4]], dtype=torch.float32)
    b = torch.tensor([[5, 6], [7, 8]], dtype=torch.float32)
    
    print(f"矩阵a: {a}")
    print(f"矩阵b: {b}")
    print(f"加法: {a + b}")
    print(f"矩阵乘法: {torch.mm(a, b)}")

# ============================================================================
# 二、前馈神经网络实现
# ============================================================================

class SimpleNeuron:
    """单个神经元的实现"""
    def __init__(self, input_size):
        # 随机初始化权重和偏置
        self.weights = torch.randn(input_size)
        self.bias = torch.randn(1)
    
    def forward(self, x):
        """前向传播：加权求和 + 激活函数"""
        # 1. 加权求和
        z = torch.dot(x, self.weights) + self.bias
        print(f"加权求和结果 z = {z.item():.4f}")
        
        # 2. 激活函数（ReLU）
        a = torch.relu(z)
        print(f"激活函数结果 a = {a.item():.4f}")
        
        return a

def neuron_demo():
    """神经元工作机制演示"""
    print("\n" + "=" * 50)
    print("神经元工作机制演示")
    print("=" * 50)
    
    # 创建一个3输入的神经元
    neuron = SimpleNeuron(3)
    print(f"权重: {neuron.weights}")
    print(f"偏置: {neuron.bias}")
    
    # 输入数据
    x = torch.tensor([1.0, 2.0, 3.0])
    print(f"输入: {x}")
    
    # 前向传播
    output = neuron.forward(x)
    print(f"最终输出: {output.item():.4f}")

class SimpleFeedForwardNet(nn.Module):
    """简单的前馈神经网络"""
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleFeedForwardNet, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        """前向传播过程"""
        print(f"输入层: {x}")
        
        # 第一层：输入 -> 隐藏层
        z1 = self.fc1(x)
        print(f"隐藏层线性变换: {z1}")
        
        h1 = torch.relu(z1)
        print(f"隐藏层激活后: {h1}")
        
        # 第二层：隐藏层 -> 输出层
        z2 = self.fc2(h1)
        print(f"输出层线性变换: {z2}")
        
        output = torch.sigmoid(z2)
        print(f"输出层激活后: {output}")
        
        return output

def feedforward_demo():
    """前馈神经网络演示"""
    print("\n" + "=" * 50)
    print("前馈神经网络演示")
    print("=" * 50)
    
    # 创建网络：3输入 -> 2隐藏 -> 1输出
    net = SimpleFeedForwardNet(3, 2, 1)
    
    # 输入数据
    x = torch.tensor([1.0, 2.0, 3.0])
    
    # 前向传播
    output = net(x)
    print(f"网络最终输出: {output.item():.4f}")

# ============================================================================
# 三、激活函数对比
# ============================================================================

def activation_functions_demo():
    """激活函数对比演示"""
    print("\n" + "=" * 50)
    print("激活函数对比演示")
    print("=" * 50)
    
    x = torch.linspace(-5, 5, 100)
    
    # 计算不同激活函数的输出
    sigmoid_out = torch.sigmoid(x)
    relu_out = torch.relu(x)
    tanh_out = torch.tanh(x)
    
    # 绘制激活函数
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(x.numpy(), sigmoid_out.numpy())
    plt.title('Sigmoid')
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.plot(x.numpy(), relu_out.numpy())
    plt.title('ReLU')
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.plot(x.numpy(), tanh_out.numpy())
    plt.title('Tanh')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('activation_functions.png')
    plt.close()
    
    print("激活函数图像已保存为 activation_functions.png")
    
    # 数值示例
    test_input = torch.tensor([-2.0, -1.0, 0.0, 1.0, 2.0])
    print(f"\n输入: {test_input}")
    print(f"Sigmoid: {torch.sigmoid(test_input)}")
    print(f"ReLU: {torch.relu(test_input)}")
    print(f"Tanh: {torch.tanh(test_input)}")

# ============================================================================
# 四、卷积神经网络实现
# ============================================================================

def conv_operation_demo():
    """卷积操作演示"""
    print("\n" + "=" * 50)
    print("卷积操作演示")
    print("=" * 50)
    
    # 创建输入图像 (1通道, 5x5)
    input_image = torch.randn(1, 1, 5, 5)
    print(f"输入图像形状: {input_image.shape}")
    print(f"输入图像:\n{input_image.squeeze()}")
    
    # 创建卷积核 (3x3)
    conv_kernel = torch.tensor([[[[1, 0, -1],
                                  [1, 0, -1],
                                  [1, 0, -1]]]], dtype=torch.float32)
    print(f"\n卷积核形状: {conv_kernel.shape}")
    print(f"卷积核:\n{conv_kernel.squeeze()}")
    
    # 执行卷积操作
    conv_output = F.conv2d(input_image, conv_kernel, padding=0)
    print(f"\n卷积输出形状: {conv_output.shape}")
    print(f"卷积输出:\n{conv_output.squeeze()}")
    
    # 池化操作
    pool_output = F.max_pool2d(conv_output, kernel_size=2)
    print(f"\n池化输出形状: {pool_output.shape}")
    print(f"池化输出:\n{pool_output.squeeze()}")

class LeNet(nn.Module):
    """LeNet-5网络实现"""
    def __init__(self, num_classes=10):
        super(LeNet, self).__init__()
        self.conv1 = nn.Conv2d(1, 6, kernel_size=5)
        self.pool1 = nn.MaxPool2d(kernel_size=2)
        self.conv2 = nn.Conv2d(6, 16, kernel_size=5)
        self.pool2 = nn.MaxPool2d(kernel_size=2)
        self.fc1 = nn.Linear(16 * 4 * 4, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, num_classes)
    
    def forward(self, x):
        print(f"输入: {x.shape}")
        
        x = self.pool1(torch.relu(self.conv1(x)))
        print(f"第一个卷积+池化: {x.shape}")
        
        x = self.pool2(torch.relu(self.conv2(x)))
        print(f"第二个卷积+池化: {x.shape}")
        
        x = x.view(-1, 16 * 4 * 4)
        print(f"展平: {x.shape}")
        
        x = torch.relu(self.fc1(x))
        print(f"第一个全连接: {x.shape}")
        
        x = torch.relu(self.fc2(x))
        print(f"第二个全连接: {x.shape}")
        
        x = self.fc3(x)
        print(f"输出: {x.shape}")
        
        return x

def lenet_demo():
    """LeNet网络演示"""
    print("\n" + "=" * 50)
    print("LeNet网络演示")
    print("=" * 50)
    
    # 创建LeNet网络
    net = LeNet()
    
    # 创建输入 (batch_size=1, channels=1, height=28, width=28)
    x = torch.randn(1, 1, 28, 28)
    
    # 前向传播
    output = net(x)
    print(f"最终输出: {output.shape}")

# ============================================================================
# 五、训练过程演示
# ============================================================================

def training_demo():
    """训练过程演示"""
    print("\n" + "=" * 50)
    print("训练过程演示")
    print("=" * 50)
    
    # 创建简单的回归任务
    # 生成数据: y = 2x + 1 + noise
    torch.manual_seed(42)
    x_train = torch.randn(100, 1)
    y_train = 2 * x_train + 1 + 0.1 * torch.randn(100, 1)
    
    # 创建简单的线性模型
    model = nn.Linear(1, 1)
    criterion = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    
    print("训练前的参数:")
    for name, param in model.named_parameters():
        print(f"{name}: {param.data}")
    
    # 训练几个epoch
    for epoch in range(5):
        # 前向传播
        y_pred = model(x_train)
        loss = criterion(y_pred, y_train)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"Epoch {epoch+1}, Loss: {loss.item():.4f}")
    
    print("\n训练后的参数:")
    for name, param in model.named_parameters():
        print(f"{name}: {param.data}")

# ============================================================================
# 主函数
# ============================================================================

def main():
    """运行所有演示"""
    print("深度学习复习代码演示")
    print("=" * 80)
    
    # 1. 张量操作
    tensor_operations_demo()
    
    # 2. 神经元机制
    neuron_demo()
    
    # 3. 前馈神经网络
    feedforward_demo()
    
    # 4. 激活函数
    activation_functions_demo()
    
    # 5. 卷积操作
    conv_operation_demo()
    
    # 6. LeNet网络
    lenet_demo()
    
    # 7. 训练过程
    training_demo()
    
    print("\n" + "=" * 80)
    print("所有演示完成！")

if __name__ == "__main__":
    main()
