"""
可视化工具模块，用于绘制预测结果和训练过程，以及计算和显示模型评价指标
"""
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from data.data_processor import denormalize_data
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import math
import matplotlib
import platform

# 设置matplotlib字体，解决中文显示问题
if platform.system() == 'Windows':
    # Windows系统
    try:
        # 尝试使用微软雅黑字体
        matplotlib.rcParams['font.family'] = ['Microsoft YaHei']
    except:
        # 如果没有微软雅黑，使用系统默认字体
        matplotlib.rcParams['font.sans-serif'] = ['SimHei']
else:
    # 其他系统
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']

# 解决负号显示问题
matplotlib.rcParams['axes.unicode_minus'] = False

def calculate_metrics(y_true, y_pred):
    """
    计算模型评价指标

    Args:
        y_true (numpy.ndarray): 真实值
        y_pred (numpy.ndarray): 预测值

    Returns:
        dict: 包含各种评价指标的字典
    """
    # 确保输入是一维数组
    y_true = y_true.flatten()
    y_pred = y_pred.flatten()

    # 计算均方误差(MSE)
    mse = mean_squared_error(y_true, y_pred)

    # 计算均方根误差(RMSE)
    rmse = math.sqrt(mse)

    # 计算平均绝对误差(MAE)
    mae = mean_absolute_error(y_true, y_pred)

    # 计算决定系数(R²)
    r2 = r2_score(y_true, y_pred)

    # 计算平均绝对百分比误差(MAPE)
    # 避免除以零
    mask = y_true != 0
    mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100

    # 计算平均相对误差(MRE)
    mre = np.mean(np.abs(y_true - y_pred) / (np.abs(y_true) + 1e-10))

    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,  # 使用R2代替R²
        'MAPE': mape,
        'MRE': mre
    }

def plot_predictions(all_y_pred, all_y_true, min_val, max_val, config, save_path=None):
    """
    绘制预测结果和真实值的对比图，并显示评价指标

    Args:
        all_y_pred (torch.Tensor): 预测结果
        all_y_true (torch.Tensor): 真实值
        min_val (numpy.ndarray): 归一化最小值
        max_val (numpy.ndarray): 归一化最大值
        config (dict): 配置字典
        save_path (str, optional): 图像保存路径，如果为None则不保存

    Returns:
        numpy.ndarray: 对齐后的预测结果
    """
    # 获取配置参数
    predict_steps = config["predict_data"]
    output_size = config["output_size"]
    file_path = config["dataroot"]
    label_cols = config["label_cols"]

    # 将张量转换为NumPy数组
    all_y_pred = all_y_pred.detach().numpy()
    all_y_true = all_y_true[:, 0, :].detach().numpy()

    # 计算时间长度
    time_len = all_y_true.shape[0] + predict_steps - 1

    # 初始化对齐后的预测结果和计数数组
    aligned_preds = np.zeros((time_len, output_size))
    counts = np.zeros((time_len, output_size))

    # 对齐预测结果
    for i in range(all_y_pred.shape[0]):
        for t in range(predict_steps):
            aligned_preds[i+t] += all_y_pred[i, t]
            counts[i+t] += 1

    # 计算平均值
    aligned_preds /= np.maximum(counts, 1)

    # 将归一化后的数据还原成原数据
    aligned_preds = denormalize_data(aligned_preds, min_val, max_val, label_cols)
    all_y_true = denormalize_data(all_y_true, min_val, max_val, label_cols)

    # 获取列名
    header = pd.read_csv(file_path, nrows=0).columns.tolist()
    header = [header[i] for i in label_cols]

    # 绘制每个输出维度的预测结果和真实值
    for i in range(output_size):
        # 计算评价指标
        # 只使用有真实值的部分进行评估
        valid_indices = np.arange(len(all_y_true))
        metrics = calculate_metrics(all_y_true[valid_indices, i], aligned_preds[valid_indices, i])

        # 创建图形
        fig, ax = plt.subplots(figsize=(14, 8))

        # 绘制预测结果和真实值
        ax.plot(all_y_true[:, i].flatten(), label=f"True Data of {header[i]}")
        ax.plot(aligned_preds[:, i].flatten(), label=f"Predicted Data of {header[i]}", linestyle="dashed")

        # 设置标题和轴标签
        ax.set_title(f"Prediction vs True Data for {header[i]}", fontsize=16)
        ax.set_xlabel("Time Steps", fontsize=12)
        ax.set_ylabel("Value", fontsize=12)
        ax.legend(fontsize=12)
        ax.grid(True)

        # 在图中添加评价指标文本框
        metrics_text = "\n".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax.text(0.05, 0.95, metrics_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', bbox=props)

        # 保存图像
        if save_path is not None:
            plt.savefig(f"{save_path}/prediction_{header[i]}_with_metrics.png", dpi=300, bbox_inches='tight')

        plt.tight_layout()
        plt.show()

        # 打印评价指标
        print(f"\n评价指标 - {header[i]}:")
        for k, v in metrics.items():
            print(f"{k}: {v:.4f}")

    return aligned_preds[-predict_steps:]


def plot_loss_curves(trainer, save_path=None):
    """
    绘制训练和测试损失曲线

    Args:
        trainer (LSTMTrainer): 训练器实例
        save_path (str, optional): 图像保存路径，如果为None则不保存
    """
    plt.figure(figsize=(12, 6))
    plt.plot(trainer.train_losses, label="Training Loss")
    plt.plot(trainer.test_losses, label="Testing Loss")
    plt.title("Loss Curves")
    plt.xlabel("Epochs")
    plt.ylabel("Loss")
    plt.legend()
    plt.grid(True)

    # 保存图像
    if save_path is not None:
        plt.savefig(f"{save_path}/loss_curves.png")

    plt.show()


def plot_future_predictions(future_preds, header, save_path=None, historical_data=None):
    """
    绘制未来预测结果，如果提供了历史数据，则同时显示历史数据和评价指标

    Args:
        future_preds (numpy.ndarray): 未来预测结果
        header (list): 列名
        save_path (str, optional): 图像保存路径，如果为None则不保存
        historical_data (tuple, optional): 包含历史数据的元组 (aligned_preds, all_y_true)
    """
    output_size = future_preds.shape[1]
    predict_steps = future_preds.shape[0]

    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 8))

    # 如果提供了历史数据，则绘制历史数据和评价指标
    if historical_data is not None:
        aligned_preds, all_y_true = historical_data

        # 确保数据维度正确
        if len(all_y_true.shape) > 1:
            history_len = len(all_y_true)

            # 绘制历史数据
            for i in range(min(output_size, all_y_true.shape[1])):
                # 绘制历史真实值
                ax.plot(range(-history_len, 0), all_y_true[:, i],
                       label=f"Historical {header[i]}", color='blue')

                # 确保aligned_preds有足够的数据
                valid_len = min(history_len, aligned_preds.shape[0])
                # 绘制历史预测值
                ax.plot(range(-valid_len, 0), aligned_preds[:valid_len, i],
                       label=f"Predicted {header[i]} (Historical)", linestyle='dashed', color='green')

            # 计算评价指标
            for i in range(min(output_size, all_y_true.shape[1])):
                valid_len = min(history_len, aligned_preds.shape[0])
                metrics = calculate_metrics(all_y_true[:valid_len, i], aligned_preds[:valid_len, i])

                # 在图中添加评价指标文本框
                metrics_text = "\n".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
                props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
                ax.text(0.05, 0.95, metrics_text, transform=ax.transAxes, fontsize=12,
                        verticalalignment='top', bbox=props)

                # 打印评价指标
                print(f"\n评价指标 - {header[i]}:")
                for k, v in metrics.items():
                    print(f"{k}: {v:.4f}")

                # 只显示第一个输出维度的评价指标
                break

    # 绘制未来预测结果
    for i in range(output_size):
        ax.plot(range(predict_steps), future_preds[:, i],
               label=f"Future Predicted {header[i]}", marker='o', color='red')

    # 添加垂直线分隔历史数据和未来预测
    if historical_data is not None:
        ax.axvline(x=0, color='black', linestyle='--')
        ax.text(0, ax.get_ylim()[1]*0.9, "Future", fontsize=12, ha='left')
        ax.text(-1, ax.get_ylim()[1]*0.9, "History", fontsize=12, ha='right')

    # 设置标题和轴标签
    ax.set_title("Time Series Prediction with Metrics", fontsize=16)
    ax.set_xlabel("Time Steps (Negative: Historical, Positive: Future)", fontsize=12)
    ax.set_ylabel("Value", fontsize=12)
    ax.legend(fontsize=10, loc='lower right')
    ax.grid(True)

    # 保存图像
    if save_path is not None:
        plt.savefig(f"{save_path}/future_predictions_with_metrics.png", dpi=300, bbox_inches='tight')

    plt.tight_layout()
    plt.show()


def plot_train_test_comparison(train_pred, train_true, test_pred, test_true, min_val, max_val, config, save_path=None):
    """
    对比训练集和测试集的预测曲线，帮助判断模型是否欠拟合或过拟合

    Args:
        train_pred (torch.Tensor): 训练集预测结果
        train_true (torch.Tensor): 训练集真实值
        test_pred (torch.Tensor): 测试集预测结果
        test_true (torch.Tensor): 测试集真实值
        min_val (numpy.ndarray): 归一化最小值
        max_val (numpy.ndarray): 归一化最大值
        config (dict): 配置字典
        save_path (str, optional): 图像保存路径，如果为None则不保存
    """
    # 获取配置参数
    label_cols = config["label_cols"]
    file_path = config["dataroot"]

    # 将张量转换为NumPy数组
    train_pred = train_pred.detach().numpy() if hasattr(train_pred, 'detach') else train_pred
    train_true = train_true.detach().numpy() if hasattr(train_true, 'detach') else train_true
    test_pred = test_pred.detach().numpy() if hasattr(test_pred, 'detach') else test_pred
    test_true = test_true.detach().numpy() if hasattr(test_true, 'detach') else test_true

    # 确保数据维度正确
    if len(train_true.shape) > 2 and train_true.shape[1] == 1:
        train_true = train_true[:, 0, :]
    if len(test_true.shape) > 2 and test_true.shape[1] == 1:
        test_true = test_true[:, 0, :]

    # 将归一化后的数据还原成原数据
    train_pred = denormalize_data(train_pred, min_val, max_val, label_cols)
    train_true = denormalize_data(train_true, min_val, max_val, label_cols)
    test_pred = denormalize_data(test_pred, min_val, max_val, label_cols)
    test_true = denormalize_data(test_true, min_val, max_val, label_cols)

    # 获取列名
    header = pd.read_csv(file_path, nrows=0).columns.tolist()
    header = [header[i] for i in label_cols]

    # 计算训练集和测试集的评价指标
    train_metrics = {}
    test_metrics = {}

    for i in range(train_true.shape[1]):
        train_metrics[header[i]] = calculate_metrics(train_true[:, i], train_pred[:, i])
        test_metrics[header[i]] = calculate_metrics(test_true[:, i], test_pred[:, i])

    # 绘制每个输出维度的对比图
    for i in range(train_true.shape[1]):
        # 创建一个2x1的子图布局
        fig, axes = plt.subplots(2, 1, figsize=(14, 12), sharex=True)

        # 绘制训练集对比图
        axes[0].plot(train_true[:, i], label=f"True (Train)", color='blue')
        axes[0].plot(train_pred[:, i], label=f"Predicted (Train)", linestyle='dashed', color='green')
        axes[0].set_title(f"Training Set: {header[i]}", fontsize=14)
        axes[0].set_ylabel("Value", fontsize=12)
        axes[0].legend(fontsize=12)
        axes[0].grid(True)

        # 在训练集图中添加评价指标文本框
        train_metrics_text = "\n".join([f"{k}: {v:.4f}" for k, v in train_metrics[header[i]].items()])
        props = dict(boxstyle='round', facecolor='lightblue', alpha=0.5)
        axes[0].text(0.05, 0.95, train_metrics_text, transform=axes[0].transAxes, fontsize=10,
                verticalalignment='top', bbox=props)

        # 绘制测试集对比图
        axes[1].plot(test_true[:, i], label=f"True (Test)", color='blue')
        axes[1].plot(test_pred[:, i], label=f"Predicted (Test)", linestyle='dashed', color='red')
        axes[1].set_title(f"Testing Set: {header[i]}", fontsize=14)
        axes[1].set_xlabel("Time Steps", fontsize=12)
        axes[1].set_ylabel("Value", fontsize=12)
        axes[1].legend(fontsize=12)
        axes[1].grid(True)

        # 在测试集图中添加评价指标文本框
        test_metrics_text = "\n".join([f"{k}: {v:.4f}" for k, v in test_metrics[header[i]].items()])
        props = dict(boxstyle='round', facecolor='salmon', alpha=0.5)
        axes[1].text(0.05, 0.95, test_metrics_text, transform=axes[1].transAxes, fontsize=10,
                verticalalignment='top', bbox=props)

        # 判断是否欠拟合或过拟合
        train_rmse = train_metrics[header[i]]['RMSE']
        test_rmse = test_metrics[header[i]]['RMSE']
        train_r2 = train_metrics[header[i]]['R2']  # 使用R2代替R²
        test_r2 = test_metrics[header[i]]['R2']    # 使用R2代替R²

        # 添加拟合状态分析
        fitting_status = ""
        if train_rmse > 0.1 * (max_val[label_cols[i]] - min_val[label_cols[i]]) and test_rmse > 0.1 * (max_val[label_cols[i]] - min_val[label_cols[i]]):
            if abs(train_rmse - test_rmse) < 0.05 * (max_val[label_cols[i]] - min_val[label_cols[i]]):
                fitting_status = "欠拟合: 训练集和测试集误差都较大，且差异不大"
            else:
                fitting_status = "欠拟合但有过拟合趋势: 训练集和测试集误差都较大，但差异明显"
        elif train_rmse < 0.05 * (max_val[label_cols[i]] - min_val[label_cols[i]]) and test_rmse > 0.1 * (max_val[label_cols[i]] - min_val[label_cols[i]]):
            fitting_status = "过拟合: 训练集误差小，测试集误差大"
        elif train_rmse < 0.05 * (max_val[label_cols[i]] - min_val[label_cols[i]]) and test_rmse < 0.05 * (max_val[label_cols[i]] - min_val[label_cols[i]]):
            fitting_status = "良好拟合: 训练集和测试集误差都小"
        else:
            fitting_status = "一般拟合: 模型性能中等"

        # 添加R2分析
        r2_analysis = ""
        if train_r2 > 0.8 and test_r2 > 0.8:
            r2_analysis = "模型解释能力强: 训练集和测试集的R2都较高"
        elif train_r2 > 0.8 and test_r2 < 0.5:
            r2_analysis = "过拟合迹象: 训练集R2高但测试集R2低"
        elif train_r2 < 0.5 and test_r2 < 0.5:
            r2_analysis = "欠拟合迹象: 训练集和测试集的R2都较低"
        else:
            r2_analysis = "模型解释能力一般"

        # 添加拟合状态文本框
        fitting_text = f"{fitting_status}\n{r2_analysis}\n\n训练集与测试集RMSE比: {train_rmse:.4f}/{test_rmse:.4f} = {train_rmse/test_rmse if test_rmse != 0 else float('inf'):.2f}"
        props = dict(boxstyle='round', facecolor='yellow', alpha=0.5)
        fig.text(0.5, 0.01, fitting_text, ha='center', fontsize=12, bbox=props)

        # 打印评价指标和拟合状态
        print(f"\n{header[i]} 拟合状态分析:")
        print(fitting_status)
        print(r2_analysis)
        print(f"训练集与测试集RMSE比: {train_rmse:.4f}/{test_rmse:.4f} = {train_rmse/test_rmse if test_rmse != 0 else float('inf'):.2f}")
        print("\n训练集评价指标:")
        for k, v in train_metrics[header[i]].items():
            print(f"{k}: {v:.4f}")
        print("\n测试集评价指标:")
        for k, v in test_metrics[header[i]].items():
            print(f"{k}: {v:.4f}")

        plt.tight_layout(rect=[0, 0.03, 1, 0.97])

        # 保存图像
        if save_path is not None:
            plt.savefig(f"{save_path}/train_test_comparison_{header[i]}.png", dpi=300, bbox_inches='tight')

        plt.show()
